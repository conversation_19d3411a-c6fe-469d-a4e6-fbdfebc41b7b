<!DOCTYPE html>
<html>
<head>
    <title>Debug Filters</title>
</head>
<body>
    <h1>Debug Mapbox Filters</h1>
    <div id="debug-output"></div>

    <script>
        // Test filter expressions
        const testFilters = {
            salesStatus: ['Available', 'Reserved'],
            priceRange: { min: 100000, max: 500000 },
            services: ['Water', 'Electricity']
        };

        // Simulate the filter building logic
        function buildFilterExpression(filters) {
            let filterExpression = ["all"];

            // Sales status filter
            if (filters.salesStatus && filters.salesStatus.length > 0) {
                const statusFilter = ["in", ["get", "sales_status"], ["literal", filters.salesStatus]];
                filterExpression.push(statusFilter);
            }

            // Price range filter
            if (filters.priceRange && filters.priceRange.min !== undefined && filters.priceRange.max !== undefined) {
                const priceFilter = [
                    "all",
                    [">=", ["get", "price_with_tax"], filters.priceRange.min],
                    ["<=", ["get", "price_with_tax"], filters.priceRange.max]
                ];
                filterExpression.push(priceFilter);
            }

            // Services filter
            if (filters.services && filters.services.length > 0) {
                const servicesFilters = filters.services.map(service => [
                    "!=", ["index-of", service, ["to-string", ["get", "services_available"]]], -1
                ]);
                if (servicesFilters.length === 1) {
                    filterExpression.push(servicesFilters[0]);
                } else if (servicesFilters.length > 1) {
                    filterExpression.push(["any", ...servicesFilters]);
                }
            }

            return filterExpression;
        }

        const result = buildFilterExpression(testFilters);
        document.getElementById('debug-output').innerHTML = '<pre>' + JSON.stringify(result, null, 2) + '</pre>';
        console.log('Filter expression:', result);
    </script>
</body>
</html>
