import React, { useState, createContext, useContext, useRef, useEffect } from "react";
import "./App.css";
import Header from "./components/Header/Header";
import OptimizedMap from "./components/Map/OptimizedMap";

import ErrorBoundary from "./components/ErrorBoundary/ErrorBoundary";

// Language Context
const LanguageContext = createContext();

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
};

function App() {
  const [selectedProject, setSelectedProject] = useState(null);
  const [sharedProjectId, setSharedProjectId] = useState(null);
  const [isSharedView, setIsSharedView] = useState(false);
  const [language, setLanguage] = useState("ar"); // 'ar' for Arabic, 'en' for English
  const [mapStyle, setMapStyle] = useState("satellite-v9"); // Default map style
  const [filtersVisible, setFiltersVisible] = useState(false);
  const [statusFilters, setStatusFilters] = useState([]); // فلاتر الحالة الجديدة
  const [priceFilter, setPriceFilter] = useState({ min: 0, max: 1000000 }); // فلتر السعر
  const [usageFilters, setUsageFilters] = useState([]); // فلاتر الاستخدام
  const parcelSearchRef = useRef(null);

  // Check for shared project in URL on component mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('project');
    const shared = urlParams.get('shared');

    if (projectId && shared === 'true') {
      const numericProjectId = parseInt(projectId);
      setSharedProjectId(numericProjectId);
      setSelectedProject(numericProjectId);
      setIsSharedView(true);
      console.log(`Shared view activated for project: ${numericProjectId}`);
    }
  }, []);

  const toggleLanguage = () => {
    setLanguage(language === "ar" ? "en" : "ar");
  };

  const changeMapStyle = (style) => {
    setMapStyle(style);
  };

  const handleParcelSearch = (parcelNumber) => {
    if (parcelSearchRef.current && typeof parcelSearchRef.current === 'function') {
      parcelSearchRef.current(parcelNumber);
    } else {
      console.warn('Parcel search function not ready yet');
    }
  };

  const toggleFilters = () => {
    setFiltersVisible(!filtersVisible);
  };

  // دمج جميع الفلاتر في كائن واحد للخريطة
  const combineFilters = () => {
    return {
      salesStatus: statusFilters,
      priceRange: priceFilter,
      services: usageFilters
    };
  };

  const handleStatusFiltersChange = (newFilters) => {
    setStatusFilters(newFilters);
    console.log('Status filters changed:', newFilters);
  };

  const handlePriceFilterChange = (newFilter) => {
    setPriceFilter(newFilter);
    console.log('Price filter changed:', newFilter);
  };

  const handleUsageFiltersChange = (newFilters) => {
    setUsageFilters(newFilters);
    console.log('Usage filters changed:', newFilters);
  };

  const languageValue = {
    language,
    toggleLanguage,
    isRTL: language === "ar",
  };

  return (
    <ErrorBoundary>
      <LanguageContext.Provider value={languageValue}>
        <div className={`App ${language === "ar" ? "rtl" : "ltr"}`}>
          <Header
            selectedProject={selectedProject}
            setSelectedProject={setSelectedProject}
            onParcelSearch={handleParcelSearch}
            onToggleFilters={toggleFilters}
            onStatusFiltersChange={handleStatusFiltersChange}
            statusFilters={statusFilters}
            onPriceFilterChange={handlePriceFilterChange}
            priceFilter={priceFilter}
            onUsageFilterChange={handleUsageFiltersChange}
            usageFilters={usageFilters}
            totalUnitsCount={1829}
            isSharedView={isSharedView}
          />
          <div className="main-content">
            <div className="map-wrapper">
              <ErrorBoundary>
                <OptimizedMap
                  selectedProject={selectedProject}
                  onProjectChange={setSelectedProject}
                  mapStyle={mapStyle}
                  onMapStyleChange={changeMapStyle}
                  filters={combineFilters()}
                  statusFilters={statusFilters}
                  priceFilter={priceFilter}
                  usageFilters={usageFilters}
                  onLanguageToggle={toggleLanguage}
                  language={language}
                  onParcelSearch={parcelSearchRef}
                  filtersVisible={filtersVisible}
                  onToggleFilters={toggleFilters}
                  isSharedView={isSharedView}
                  sharedProjectId={sharedProjectId}
                />
              </ErrorBoundary>
            </div>
          </div>
        </div>
      </LanguageContext.Provider>
    </ErrorBoundary>
  );
}

export default App;
