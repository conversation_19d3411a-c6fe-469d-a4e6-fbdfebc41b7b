import axios from "axios";
import { API_CONFIG } from "../config/constants";

const api = axios.create({
  baseURL: API_CONFIG.FULL_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Projects
export const getProjects = () => api.get("/projects");
export const getProject = (id) => api.get(`/projects/${id}`);

// Blocks
export const getBlocks = (projectId) =>
  api.get("/blocks", { params: { project_id: projectId } });
export const getBlock = (id) => api.get(`/blocks/${id}`);
export const getBlocksGeoJson = (projectId) =>
  api.get(`/projects/${projectId}/blocks/geojson`);

// Parcels
export const getParcels = (blockId) =>
  api.get("/parcels", { params: { block_id: blockId } });
export const getParcel = (id) => api.get(`/parcels/${id}`);
export const getParcelsGeoJson = (blockId) =>
  api.get(`/blocks/${blockId}/parcels/geojson`);
export const searchParcels = (query) =>
  api.get("/parcels/search", { params: { q: query } });

// Map Layers
export const getMapLayers = (projectId) =>
  api.get("/map-layers", { params: { project_id: projectId } });
export const getMapLayer = (id) => api.get(`/map-layers/${id}`);
export const getProjectMapLayers = (projectId) =>
  api.get(`/projects/${projectId}/map-layers`);

// Landmarks
export const getLandmarks = (projectId) =>
  api.get("/landmarks", { params: { project_id: projectId } });
export const getLandmark = (id) => api.get(`/landmarks/${id}`);
export const getProjectLandmarks = (projectId) =>
  api.get(`/projects/${projectId}/landmarks`);

export default api;
