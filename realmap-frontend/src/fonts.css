/* 
 * Google Fonts Integration for RealMap Frontend
 * Cairo and Fira Sans fonts applied throughout the application
 */

/* Import Google Fonts - Cairo and Fira Sans */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900;1000&family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/* Global font application */
* {
  font-family: 'Cairo', 'Fira Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Droid Sans', 'Helvetica Neue', sans-serif !important;
}

/* Arabic text priority */
:lang(ar), [lang="ar"], .arabic, .rtl {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* English text and UI elements */
:lang(en), [lang="en"], .english, .ltr {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
}

/* Headings - Cairo font for better Arabic support */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
  font-weight: 600;
}

/* Body text - Cairo for main content */
p, span, div, article, section {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* UI Elements - Fira Sans for better readability */
button, input, select, textarea, label {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
}

/* Navigation and menus */
nav, .nav, .navbar, .menu, .dropdown {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
}

/* Tables */
table, th, td {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* Lists */
ul, ol, li, dl, dt, dd {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* Links */
a {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* Form elements specific styling */
input[type="text"], 
input[type="email"], 
input[type="password"], 
input[type="search"], 
input[type="tel"], 
input[type="url"], 
input[type="number"] {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
}

/* Buttons and interactive elements */
.btn, .button, [role="button"], .clickable {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
  font-weight: 500;
}

/* Cards and containers */
.card, .container, .wrapper, .box {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* Modals and overlays */
.modal, .overlay, .popup, .tooltip, .popover {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* Specific component classes */
.parcel-card, .project-selector, .header, .sidebar {
  font-family: 'Cairo', 'Fira Sans', sans-serif !important;
}

/* Map related elements */
.mapboxgl-popup, .mapboxgl-ctrl, .map-control {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
}

/* Status and badge elements */
.status, .badge, .tag, .chip {
  font-family: 'Fira Sans', 'Cairo', sans-serif !important;
  font-weight: 600;
}

/* Code elements - keep monospace */
code, pre, .code {
  font-family: 'Fira Code', 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
}

/* Ensure font smoothing */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Font weight classes */
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* Arabic specific font weights */
.arabic .font-light { font-weight: 400; }
.arabic .font-normal { font-weight: 500; }
.arabic .font-medium { font-weight: 600; }
.arabic .font-semibold { font-weight: 700; }
.arabic .font-bold { font-weight: 800; }

/* Responsive font sizes */
@media (max-width: 768px) {
  h1 { font-size: 1.8rem; }
  h2 { font-size: 1.5rem; }
  h3 { font-size: 1.3rem; }
  h4 { font-size: 1.1rem; }
  h5 { font-size: 1rem; }
  h6 { font-size: 0.9rem; }
}

@media (max-width: 480px) {
  h1 { font-size: 1.6rem; }
  h2 { font-size: 1.3rem; }
  h3 { font-size: 1.1rem; }
  h4 { font-size: 1rem; }
  h5 { font-size: 0.9rem; }
  h6 { font-size: 0.8rem; }
}
