body {
  margin: 0;
  font-family: 'Cairo', 'Fira Sans', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Font family definitions for different elements */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 600;
}

/* UI elements use Fira Sans for better readability */
button, input, select, textarea, .ui-element {
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 400;
}

/* Arabic text and main content use Cairo */
.arabic-text, .main-content {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Labels and important text */
.label, .important-text {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 500;
}

/* Apply fonts to all text elements */
p, span, div, a, li, td, th {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Form elements */
input, textarea, select, option {
  font-family: 'Fira Sans', 'Cairo', sans-serif;
}

/* Navigation and menu items */
nav, .nav, .menu, .dropdown {
  font-family: 'Fira Sans', 'Cairo', sans-serif;
}

/* Cards and containers */
.card, .container, .wrapper {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Buttons and interactive elements */
.btn, .button, [role="button"] {
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 500;
}

/* Tooltips and popovers */
.tooltip, .popover, .modal {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Tables */
table, .table {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Lists */
ul, ol, dl {
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Ensure all text inherits the font */
* {
  font-family: inherit;
}
