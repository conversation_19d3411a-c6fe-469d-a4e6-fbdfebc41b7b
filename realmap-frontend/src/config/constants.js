/**
 * Application Configuration Constants
 * 
 * This file contains all the configuration constants used throughout the application.
 * Update these values to change URLs, API endpoints, and other configuration settings.
 */

// Environment-based configuration
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// API Configuration
export const API_CONFIG = {
  // Base API URL - can be overridden by environment variable
  BASE_URL: process.env.REACT_APP_API_URL || 'http://localhost:8001',
  
  // API endpoints
  ENDPOINTS: {
    API_BASE: '/api',
    PROJECTS: '/projects',
    BLOCKS: '/blocks',
    PARCELS: '/parcels',
    MAP_LAYERS: '/map-layers',
    LANDMARKS: '/landmarks',
    STORAGE: '/storage',
  },
  
  // Full API URL
  get FULL_URL() {
    return `${this.BASE_URL}${this.ENDPOINTS.API_BASE}`;
  },
  
  // Storage URL for file access
  get STORAGE_URL() {
    return `${this.BASE_URL}${this.ENDPOINTS.STORAGE}`;
  },
  
  // Map layers file access URL
  get MAP_LAYERS_FILE_URL() {
    return `${this.BASE_URL}${this.ENDPOINTS.API_BASE}/map-layers/file`;
  }
};

// Mapbox Configuration
export const MAPBOX_CONFIG = {
  // Mapbox access token
  ACCESS_TOKEN: process.env.REACT_APP_MAPBOX_TOKEN || 
    "pk.eyJ1IjoiaWRlemluZWxhYiIsImEiOiJjbWFqdHVyY28wazJoMnBzaXlxaG93bnZpIn0.DNiC2WOG_d5e_xCCtJMlkA",
  
  // Default map settings
  DEFAULT_CENTER: [45.0792, 23.8859], // Saudi Arabia center
  DEFAULT_ZOOM: 6,
  
  // Map styles
  STYLES: {
    SATELLITE: 'satellite-v9',
    STREETS: 'streets-v11',
    OUTDOORS: 'outdoors-v11'
  },
  
  // Map style URLs
  get STYLE_URLS() {
    return {
      SATELLITE: `mapbox://styles/mapbox/${this.STYLES.SATELLITE}`,
      STREETS: `mapbox://styles/mapbox/${this.STYLES.STREETS}`,
      OUTDOORS: `mapbox://styles/mapbox/${this.STYLES.OUTDOORS}`
    };
  }
};

// Application URLs and Paths
export const APP_PATHS = {
  // Public assets
  PUBLIC_ASSETS: {
    LOGO: '/logo.png',
    LOGO_ONLY: '/logo_only.png',
    MAP_ICONS: '/map_icons',
  },
  
  // Map icon paths
  MAP_ICONS: {
    MOSQUE: '/map_icons/Mosque icon.png',
    SCHOOL: '/map_icons/School icon.png',
    HOSPITAL: '/map_icons/Medical center.png',
    SHOPPING_CENTER: '/map_icons/Socity center icon.png',
    PARK: '/map_icons/Green Area icon.png',
    GAS_STATION: '/map_icons/Parking icon.png',
    RESTAURANT: '/map_icons/Socity center icon.png',
    BANK: '/map_icons/Socity center icon.png',
    PHARMACY: '/map_icons/Clinic icon.png',
    OTHER: '/map_icons/Socity center icon.png',
    
    // Default fallback icon
    get DEFAULT() {
      return this.OTHER;
    }
  }
};

// Application Settings
export const APP_SETTINGS = {
  // Default language
  DEFAULT_LANGUAGE: 'ar',
  
  // Supported languages
  SUPPORTED_LANGUAGES: ['ar', 'en'],
  
  // Default map style
  DEFAULT_MAP_STYLE: MAPBOX_CONFIG.STYLES.SATELLITE,
  
  // Loading and animation settings
  LOADING_TIMEOUT: 2000,
  ANIMATION_DURATION: 400,
  
  // Map settings
  MAP: {
    MIN_ZOOM: 1,
    MAX_ZOOM: 22,
    LANDMARK_MIN_ZOOM: 14,
    PROJECT_MARKER_HIDE_ZOOM: 12,
  }
};

// Development/Production specific settings
export const ENV_CONFIG = {
  IS_DEVELOPMENT: isDevelopment,
  IS_PRODUCTION: isProduction,
  
  // Enable/disable features based on environment
  FEATURES: {
    DEBUG_MODE: isDevelopment,
    CONSOLE_LOGS: isDevelopment,
    ERROR_REPORTING: isProduction,
  }
};

// Helper functions
export const getApiUrl = (endpoint = '') => {
  return `${API_CONFIG.FULL_URL}${endpoint}`;
};

export const getStorageUrl = (path = '') => {
  return `${API_CONFIG.STORAGE_URL}/${path}`;
};

export const getMapLayerFileUrl = (filename) => {
  return `${API_CONFIG.MAP_LAYERS_FILE_URL}/${filename}`;
};

export const getMapIconPath = (iconType) => {
  return APP_PATHS.MAP_ICONS[iconType.toUpperCase()] || APP_PATHS.MAP_ICONS.DEFAULT;
};

// Export default configuration object
export default {
  API_CONFIG,
  MAPBOX_CONFIG,
  APP_PATHS,
  APP_SETTINGS,
  ENV_CONFIG,
  // Helper functions
  getApiUrl,
  getStorageUrl,
  getMapLayerFileUrl,
  getMapIconPath
};
