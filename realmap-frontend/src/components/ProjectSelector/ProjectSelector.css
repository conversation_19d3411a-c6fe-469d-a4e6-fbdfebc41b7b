.project-selector {
  display: flex;
  align-items: center;
  width: 100%;
}

.project-selector label {
  margin-right: 10px;
  font-weight: bold;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  white-space: nowrap;
}

.project-selector select {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 400;
  min-width: 200px;
}

.project-selector.loading,
.project-selector.error,
.project-selector.empty {
  padding: 8px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  font-style: italic;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.project-selector.error {
  color: #ff6b6b;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .project-selector {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .project-selector label {
    margin-right: 0;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .project-selector select {
    min-width: auto;
    width: 100%;
    padding: 12px 16px;
    font-size: 16px; /* Prevents zoom on iOS */
    min-height: 44px;
    touch-action: manipulation;
  }

  .project-selector.loading,
  .project-selector.error,
  .project-selector.empty {
    padding: 12px 16px;
    font-size: 14px;
    min-height: 44px;
  }
}

@media (max-width: 480px) {
  .project-selector label {
    font-size: 13px;
  }

  .project-selector select {
    padding: 10px 14px;
    font-size: 15px;
  }
}

/* RTL Support */
.rtl .project-selector label {
  margin-right: 0;
  margin-left: 10px;
}

@media (max-width: 768px) {
  .rtl .project-selector label {
    margin-left: 0;
    margin-bottom: 4px;
  }
}
