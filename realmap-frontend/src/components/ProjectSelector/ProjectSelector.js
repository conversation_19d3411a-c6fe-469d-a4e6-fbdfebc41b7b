import React, { useEffect, useState } from "react";
import "./ProjectSelector.css";
import { getProjects } from "../../services/api";
import { getTranslation } from "../../utils/translations";
import { useLanguage } from "../../App";

const ProjectSelector = ({ selectedProject, setSelectedProject }) => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const { language } = useLanguage();

  useEffect(() => {
    const loadProjects = async () => {
      try {
        setLoading(true);
        const response = await getProjects();
        setProjects(response.data.data);

        // If no project is selected and we have projects, select the first one
        if (!selectedProject && response.data.data.length > 0) {
          setSelectedProject(response.data.data[0].id);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error loading projects:", error);
        setError("Failed to load projects");
        setLoading(false);
      }
    };

    loadProjects();
  }, [selectedProject, setSelectedProject]);

  const handleProjectChange = (e) => {
    setSelectedProject(parseInt(e.target.value));
  };

  if (loading) {
    return (
      <div className="project-selector loading">
        {getTranslation("loadingProjects", language)}
      </div>
    );
  }

  if (error) {
    return <div className="project-selector error">{error}</div>;
  }

  if (projects.length === 0) {
    return (
      <div className="project-selector empty">
        {getTranslation("noDataAvailable", language)}
      </div>
    );
  }

  return (
    <div className="project-selector">
      <label htmlFor="project-select">
        {getTranslation("selectProject", language)}:
      </label>
      <select
        id="project-select"
        value={selectedProject || ""}
        onChange={handleProjectChange}
      >
        <option value="" disabled>
          {getTranslation("selectProjectPlaceholder", language)}
        </option>
        {projects.map((project) => (
          <option key={project.id} value={project.id}>
            {project.name}
          </option>
        ))}
      </select>
    </div>
  );
};

export default ProjectSelector;
