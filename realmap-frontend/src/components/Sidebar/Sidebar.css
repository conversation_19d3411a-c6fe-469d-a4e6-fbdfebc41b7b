.sidebar {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  border-left: 1px solid #ddd;
  overflow: hidden;
  transition: transform 0.3s ease-in-out;
}

.sidebar.closed {
  transform: translateX(100%);
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #ddd;
  background-color: #fff;
  padding-right: 10px;
}

.sidebar-tabs {
  display: flex;
  flex: 1;
}

.sidebar-tabs button {
  flex: 1;
  padding: 10px;
  background-color: #f5f5f5;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  transition: background-color 0.3s;
}

.sidebar-tabs button.active {
  background-color: #fff;
  border-bottom: 2px solid #1f2937;
}

.sidebar-tabs button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.blocks-list h3,
.parcels-list h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 600;
}

.blocks-list ul,
.parcels-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.blocks-list li,
.parcels-list li {
  padding: 8px 10px;
  margin-bottom: 5px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.blocks-list li:hover,
.parcels-list li:hover {
  background-color: #f0f0f0;
}

.blocks-list li.selected,
.parcels-list li.selected {
  background-color: #e6f2ff;
  border-color: #1f2937;
}

.parcels-list li.status-available {
  border-left: 4px solid #4caf50;
}

.parcels-list li.status-reserved {
  border-left: 4px solid #ff9800;
}

.parcels-list li.status-sold {
  border-left: 4px solid #f44336;
}

.block-details,
.parcel-details {
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
}

.block-details h3,
.parcel-details h3 {
  margin-top: 0;
  margin-bottom: 10px;
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
}

.block-details table,
.parcel-details table {
  width: 100%;
  border-collapse: collapse;
}

.block-details td,
.parcel-details td {
  padding: 5px;
  border-bottom: 1px solid #eee;
}

.block-details td:first-child,
.parcel-details td:first-child {
  font-weight: bold;
  width: 40%;
}

.status-available {
  color: #4caf50;
}

.status-reserved {
  color: #ff9800;
}

.status-sold {
  color: #f44336;
}

/* Parcel item styling with map button */
.parcel-item {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px !important;
}

.parcel-info {
  flex: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.parcel-number {
  font-weight: bold;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: #333;
}

.parcel-status {
  color: #666;
  margin-left: 4px;
}

.show-on-map-btn {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 14px;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 500;
  margin-left: 8px;
  transition: all 0.2s;
  min-width: 30px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.show-on-map-btn:hover {
  background: #218838;
  transform: scale(1.05);
}

.parcel-item.selected .show-on-map-btn {
  background: #ffc107;
  color: #000;
}

.parcel-item.selected .show-on-map-btn:hover {
  background: #e0a800;
}

.parcel-item:hover .show-on-map-btn {
  background: #20c997;
}

/* Block item styling with map button */
.block-item {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
  padding: 8px 10px !important;
}

.block-info {
  flex: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.block-number {
  font-weight: bold;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: #333;
}

.block-item:hover .show-on-map-btn {
  background: #20c997;
}

.block-item.selected .show-on-map-btn {
  background: #ffc107;
  color: #000;
}

.block-item.selected .show-on-map-btn:hover {
  background: #e0a800;
}

/* Sidebar close button */
.sidebar-close-btn {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: all 0.2s ease;
  min-width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-close-btn:hover {
  background: #c82333;
  transform: scale(1.05);
}

.sidebar-close-btn:active {
  transform: scale(0.95);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .sidebar-header {
    padding-right: 8px;
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .sidebar-close-btn {
    padding: 6px 10px;
    min-width: 32px;
    height: 32px;
    font-size: 12px;
    touch-action: manipulation;
  }

  .sidebar-tabs button {
    padding: 12px 8px;
    font-size: 14px;
    min-height: 44px;
    touch-action: manipulation;
  }

  .sidebar-content {
    padding: 15px;
    -webkit-overflow-scrolling: touch;
  }

  /* Make list items more touch-friendly */
  .blocks-list li,
  .parcels-list li {
    padding: 12px 15px;
    margin-bottom: 8px;
    min-height: 44px;
    display: flex;
    align-items: center;
  }

  .show-on-map-btn {
    min-width: 44px;
    height: 44px;
    font-size: 16px;
    touch-action: manipulation;
  }
}

/* RTL Support */
.rtl .sidebar {
  border-left: none;
  border-right: 1px solid #ddd;
}

.rtl .sidebar.closed {
  transform: translateX(-100%);
}

.rtl .sidebar-header {
  padding-right: 0;
  padding-left: 10px;
}

@media (max-width: 768px) {
  .rtl .sidebar-header {
    padding-left: 8px;
  }
}
