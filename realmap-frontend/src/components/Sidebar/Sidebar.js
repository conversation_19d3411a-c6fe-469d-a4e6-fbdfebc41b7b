import React, { useEffect, useState } from "react";
import "./Sidebar.css";
import { getBlock, getBlocks, getParcel, getParcels } from "../../services/api";
import { getTranslation } from "../../utils/translations";
import { useLanguage } from "../../App";

const Sidebar = ({
  selectedProject,
  selectedBlock,
  setSelectedBlock,
  selectedParcel,
  setSelectedParcel,
  mapFunctions,
  isOpen = true,
  onClose,
}) => {
  const [blocks, setBlocks] = useState([]);
  const [parcels, setParcels] = useState([]);
  const [blockDetails, setBlockDetails] = useState(null);
  const [parcelDetails, setParcelDetails] = useState(null);
  const [activeTab, setActiveTab] = useState("blocks");
  const { language } = useLanguage();

  // Load blocks when project changes
  useEffect(() => {
    if (!selectedProject) return;

    const loadBlocks = async () => {
      try {
        const response = await getBlocks(selectedProject);
        setBlocks(response.data.data);
      } catch (error) {
        console.error("Error loading blocks:", error);
      }
    };

    loadBlocks();
    setSelectedBlock(null);
    setSelectedParcel(null);
    setBlockDetails(null);
    setParcelDetails(null);
    setParcels([]);
  }, [selectedProject, setSelectedBlock, setSelectedParcel]);

  // Load block details when selected block changes
  useEffect(() => {
    if (!selectedBlock) {
      setBlockDetails(null);
      setParcels([]);
      setSelectedParcel(null);
      setParcelDetails(null);
      return;
    }

    const loadBlockDetails = async () => {
      try {
        const response = await getBlock(selectedBlock);
        setBlockDetails(response.data.data);
      } catch (error) {
        console.error("Error loading block details:", error);
      }
    };

    const loadParcels = async () => {
      try {
        const response = await getParcels(selectedBlock);
        setParcels(response.data.data);
      } catch (error) {
        console.error("Error loading parcels:", error);
      }
    };

    loadBlockDetails();
    loadParcels();
    setSelectedParcel(null);
    setParcelDetails(null);
    setActiveTab("parcels");
  }, [selectedBlock, setSelectedParcel]);

  // Load parcel details when selected parcel changes
  useEffect(() => {
    if (!selectedParcel) {
      setParcelDetails(null);
      return;
    }

    const loadParcelDetails = async () => {
      try {
        // Find the parcel ID from the parcels array
        const parcel = parcels.find((p) => p.parcel_number === selectedParcel);
        if (parcel) {
          const response = await getParcel(parcel.id);
          setParcelDetails(response.data.data);
        }
      } catch (error) {
        console.error("Error loading parcel details:", error);
      }
    };

    loadParcelDetails();
  }, [selectedParcel, parcels]);

  const handleBlockClick = (blockId) => {
    console.log("handleBlockClick called with:", blockId);
    console.log("mapFunctions:", mapFunctions);

    setSelectedBlock(blockId);
    // Zoom to block on map
    if (mapFunctions && mapFunctions.zoomToBlock) {
      console.log("Calling zoomToBlock with ID:", blockId);
      mapFunctions.zoomToBlock(blockId);
    } else {
      console.log("Cannot zoom to block:", {
        mapFunctions: !!mapFunctions,
        zoomToBlock: !!(mapFunctions && mapFunctions.zoomToBlock),
      });
    }
  };

  const handleParcelClick = (parcelNumber) => {
    setSelectedParcel(parcelNumber);
  };

  const handleShowParcelOnMap = (parcelNumber) => {
    console.log("handleShowParcelOnMap called with:", parcelNumber);
    console.log("mapFunctions:", mapFunctions);
    console.log("parcels:", parcels);

    // Find parcel ID and zoom to it
    const parcel = parcels.find((p) => p.parcel_number === parcelNumber);
    console.log("Found parcel:", parcel);

    if (parcel && mapFunctions && mapFunctions.zoomToParcel) {
      console.log("Calling zoomToParcel with ID:", parcel.id);
      mapFunctions.zoomToParcel(parcel.id);
      // Also select the parcel
      setSelectedParcel(parcelNumber);
    } else {
      console.log("Cannot zoom to parcel:", {
        parcel: !!parcel,
        mapFunctions: !!mapFunctions,
        zoomToParcel: !!(mapFunctions && mapFunctions.zoomToParcel),
      });
    }
  };

  const renderBlocksList = () => {
    return (
      <div className="blocks-list">
        <h3>Blocks</h3>
        {blocks.length === 0 ? (
          <p>No blocks available</p>
        ) : (
          <ul>
            {blocks.map((block) => (
              <li
                key={block.id}
                className={`block-item ${
                  selectedBlock === block.id ? "selected" : ""
                }`}
              >
                <div
                  className="block-info"
                  onClick={() => handleBlockClick(block.id)}
                >
                  <span className="block-number">
                    Block {block.block_number}
                  </span>
                </div>
                <button
                  className="show-on-map-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleBlockClick(block.id);
                  }}
                  title="عرض في الخريطة"
                >
                  📍
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };

  const renderParcelsList = () => {
    return (
      <div className="parcels-list">
        <h3>Parcels in Block {blockDetails?.block_number}</h3>
        {parcels.length === 0 ? (
          <p>No parcels available</p>
        ) : (
          <ul>
            {parcels.map((parcel) => (
              <li
                key={parcel.id}
                className={`parcel-item ${
                  selectedParcel === parcel.parcel_number ? "selected" : ""
                } status-${parcel.sales_status.toLowerCase()}`}
              >
                <div
                  className="parcel-info"
                  onClick={() => handleParcelClick(parcel.parcel_number)}
                >
                  <span className="parcel-number">{parcel.parcel_number}</span>
                  <span className="parcel-status">
                    {" "}
                    - {parcel.sales_status}
                  </span>
                </div>
                <button
                  className="show-on-map-btn"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleShowParcelOnMap(parcel.parcel_number);
                  }}
                  title="عرض في الخريطة"
                >
                  📍
                </button>
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  };

  const renderBlockDetails = () => {
    if (!blockDetails) return null;

    return (
      <div className="block-details">
        <h3>Block Details</h3>
        <table>
          <tbody>
            <tr>
              <td>Block Number:</td>
              <td>{blockDetails.block_number}</td>
            </tr>
            <tr>
              <td>Shape Area:</td>
              <td>{blockDetails.shape_area} m²</td>
            </tr>
            <tr>
              <td>Shape Length:</td>
              <td>{blockDetails.shape_length} m</td>
            </tr>
            <tr>
              <td>Parcels Count:</td>
              <td>{parcels.length}</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  };

  const renderParcelDetails = () => {
    if (!parcelDetails) return null;

    return (
      <div className="parcel-details">
        <h3>Parcel Details</h3>
        <table>
          <tbody>
            <tr>
              <td>Parcel Number:</td>
              <td>{parcelDetails.parcel_number}</td>
            </tr>
            <tr>
              <td>Status:</td>
              <td
                className={`status-${parcelDetails.sales_status.toLowerCase()}`}
              >
                {parcelDetails.sales_status}
              </td>
            </tr>
            <tr>
              <td>Area:</td>
              <td>{parcelDetails.area} m²</td>
            </tr>
            {parcelDetails.price_per_sqm && (
              <tr>
                <td>Price per m²:</td>
                <td>{parcelDetails.price_per_sqm} AED</td>
              </tr>
            )}
            {parcelDetails.price_with_tax && (
              <tr>
                <td>Total Price:</td>
                <td>{parcelDetails.price_with_tax} AED</td>
              </tr>
            )}
            {parcelDetails.property_type && (
              <tr>
                <td>Property Type:</td>
                <td>{parcelDetails.property_type}</td>
              </tr>
            )}
            {parcelDetails.usage_type && (
              <tr>
                <td>Usage Type:</td>
                <td>{parcelDetails.usage_type}</td>
              </tr>
            )}
            {parcelDetails.plot_condition && (
              <tr>
                <td>Plot Condition:</td>
                <td>{parcelDetails.plot_condition}</td>
              </tr>
            )}
            {parcelDetails.view && (
              <tr>
                <td>View:</td>
                <td>{parcelDetails.view}</td>
              </tr>
            )}
            {parcelDetails.expected_roi && (
              <tr>
                <td>Expected ROI:</td>
                <td>{parcelDetails.expected_roi}</td>
              </tr>
            )}
            {parcelDetails.delivery_date && (
              <tr>
                <td>Delivery Date:</td>
                <td>
                  {new Date(parcelDetails.delivery_date).toLocaleDateString()}
                </td>
              </tr>
            )}
            {parcelDetails.down_payment && (
              <tr>
                <td>Down Payment:</td>
                <td>{parcelDetails.down_payment}</td>
              </tr>
            )}
            {parcelDetails.payment_terms && (
              <tr>
                <td>Payment Terms:</td>
                <td>{parcelDetails.payment_terms}</td>
              </tr>
            )}
            {parcelDetails.infrastructure_status && (
              <tr>
                <td>Infrastructure:</td>
                <td>{parcelDetails.infrastructure_status}</td>
              </tr>
            )}
            {parcelDetails.services_available && (
              <tr>
                <td>Services:</td>
                <td>{parcelDetails.services_available}</td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    );
  };

  return (
    <div className={`sidebar ${isOpen ? "open" : "closed"}`}>
      <div className="sidebar-header">
        <div className="sidebar-tabs">
          <button
            className={activeTab === "blocks" ? "active" : ""}
            onClick={() => setActiveTab("blocks")}
          >
            {getTranslation("blocks", language)}
          </button>
          <button
            className={activeTab === "parcels" ? "active" : ""}
            onClick={() => setActiveTab("parcels")}
            disabled={!selectedBlock}
          >
            {getTranslation("parcels", language)}
          </button>
          <button
            className={activeTab === "details" ? "active" : ""}
            onClick={() => setActiveTab("details")}
            disabled={!selectedParcel}
          >
            {getTranslation("details", language)}
          </button>
        </div>
        <button
          className="sidebar-close-btn"
          onClick={onClose}
          title={getTranslation("closeSidebar", language)}
        >
          ✕
        </button>
      </div>
      <div className="sidebar-content">
        {activeTab === "blocks" && renderBlocksList()}
        {activeTab === "parcels" && renderParcelsList()}
        {activeTab === "details" && (
          <>
            {renderBlockDetails()}
            {renderParcelDetails()}
          </>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
