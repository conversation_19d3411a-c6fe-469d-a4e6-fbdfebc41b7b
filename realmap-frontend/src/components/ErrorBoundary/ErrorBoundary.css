.error-boundary {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px;
}

.error-content {
  text-align: center;
  max-width: 600px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.error-content h2 {
  color: #dc3545;
  margin-bottom: 16px;
  font-size: 24px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 600;
}

.error-content p {
  color: #6c757d;
  margin-bottom: 24px;
  font-size: 16px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 400;
}

.retry-button {
  background-color: #1f2937;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #106ebe;
}

.error-details {
  margin-top: 24px;
  text-align: left;
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.error-details summary {
  cursor: pointer;
  font-weight: bold;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  margin-bottom: 12px;
  color: #495057;
}

.error-details pre {
  background-color: #e9ecef;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  font-size: 12px;
  color: #495057;
  margin: 8px 0;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .error-boundary {
    padding: 16px;
  }
  
  .error-content {
    padding: 24px 20px;
  }
  
  .error-content h2 {
    font-size: 20px;
  }
  
  .error-content p {
    font-size: 14px;
  }
  
  .retry-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}
