.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.spinner-container {
  position: relative;
  display: inline-block;
}

.spinner {
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #1f2937; 
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-logo {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  object-fit: contain;
  z-index: 1;
}

.loading-spinner.small .spinner {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.loading-spinner.small .spinner-logo {
  width: 10px;
  height: 10px;
}

.loading-spinner.medium .spinner {
  width: 40px;
  height: 40px;
  border-width: 3px;
}

.loading-spinner.medium .spinner-logo {
  width: 20px;
  height: 20px;
}

.loading-spinner.large .spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

.loading-spinner.large .spinner-logo {
  width: 30px;
  height: 30px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .spinner {
    border-color: rgba(255, 255, 255, 0.1);
  }
}
