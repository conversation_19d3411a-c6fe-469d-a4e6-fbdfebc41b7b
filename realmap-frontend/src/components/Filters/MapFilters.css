.map-filters-overlay {
  position: absolute;
  top: 80px; /* Below header */
  left: 50%;
  transform: translateX(-50%);
  z-index: 1003;
  width: auto;
  max-width: 600px;
  min-width: 400px;
}

.map-filters-container {
  /* background: rgba(31, 41, 55, 0.95); */
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  /* backdrop-filter: blur(15px); */
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsive filters container */
@media (max-width: 768px) {
  .map-filters-overlay {
    top: 60px; /* Below mobile header */
    max-width: calc(100% - 20px);
    min-width: 300px;
  }

  .map-filters-container {
    border-radius: 8px;
  }

  .filters-header {
    padding: 10px 12px;
  }

  .filters-title {
    font-size: 14px;
  }

  .filters-content {
    padding: 12px;
  }

  .filter-section {
    margin-bottom: 12px;
  }

  .filter-section-title {
    font-size: 13px;
    margin-bottom: 6px;
  }
}

@media (max-width: 480px) {
  .map-filters-overlay {
    top: 50px; /* Below smaller mobile header */
    max-width: calc(100% - 10px);
    min-width: 280px;
  }

  .map-filters-container {
    border-radius: 6px;
  }

  .filters-header {
    padding: 8px 10px;
  }

  .filters-title {
    font-size: 13px;
  }

  .filter-icon {
    font-size: 12px;
  }

  .filters-content {
    padding: 10px;
  }

  .filter-section {
    margin-bottom: 10px;
  }

  .filter-section-title {
    font-size: 12px;
    margin-bottom: 4px;
  }
}

.filters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(16, 185, 129, 0.1);
}

.filters-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: white;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.filter-icon {
  color: #10b981;
  font-size: 14px;
}

.filters-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-filters-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #9ca3af;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.clear-filters-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.close-filters-btn {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #9ca3af;
  padding: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  min-height: 28px;
  font-size: 12px;
}

.close-filters-btn:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.filters-content {
  padding: 16px;
}

.filter-section {
  margin-bottom: 16px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section-title {
  color: white;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 8px 0;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

/* Status Filters */
.status-filters {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* Mobile responsive status filters */
@media (max-width: 768px) {
  .status-filters {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .status-filters {
    gap: 6px;
    flex-direction: column;
  }
}

.status-btn {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px 12px;
  border: 2px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  min-width: 100px;
  flex: 1;
  gap: 6px;
}

/* Mobile responsive status buttons */
@media (max-width: 768px) {
  .status-btn {
    padding: 6px 8px;
    min-width: 80px;
    border-radius: 4px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .status-btn {
    padding: 6px 8px;
    min-width: 70px;
    border-radius: 4px;
    font-size: 11px;
    gap: 4px;
  }
}

.status-btn.available {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-color: rgba(16, 185, 129, 0.3);
}

.status-btn.available.active {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border-color: white;
  border-width: 4px;
}

.status-btn.reserved {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border-color: rgba(245, 158, 11, 0.3);
}

.status-btn.reserved.active {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border-color: white;
  border-width: 4px;
}

.status-btn.sold {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.status-btn.sold.active {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: white;
  border-width: 4px;
}

.status-icon {
  font-size: 14px;
  margin-bottom: 0;
  flex-shrink: 0;
}

.status-label {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 0;
  flex: 1;
}

.status-count {
  font-size: 11px;
  font-weight: 500;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* Mobile responsive status elements */
@media (max-width: 768px) {
  .status-icon {
    font-size: 12px;
    margin-bottom: 0;
  }

  .status-label {
    font-size: 12px;
    margin-bottom: 0;
  }

  .status-count {
    font-size: 10px;
    margin-left: auto;
  }
}

@media (max-width: 480px) {
  .status-icon {
    font-size: 11px;
  }

  .status-label {
    font-size: 11px;
  }

  .status-count {
    font-size: 10px;
  }
}

/* Price Filter */
.price-filter {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.price-inputs {
  display: flex;
  gap: 15px;
}

.price-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.price-input-group label {
  color: #9ca3af;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.price-input-group input {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  backdrop-filter: blur(10px);
}

.price-input-group input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(15px);
}

.price-slider {
  position: relative;
  height: 20px;
}

.slider {
  position: absolute;
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: #4b5563;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #10b981;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Services Filter */
.services-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.service-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: #9ca3af;
  padding: 6px 10px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 11px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.service-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-color: rgba(255, 255, 255, 0.3);
}

.service-btn.active {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

/* Additional mobile optimizations */
@media (max-width: 480px) {
  .price-inputs {
    gap: 8px;
  }

  .price-input-group input {
    padding: 4px 6px;
    font-size: 11px;
  }

  .price-input-group label {
    font-size: 11px;
  }

  .services-filter {
    gap: 6px;
  }

  .service-btn {
    padding: 4px 8px;
    font-size: 10px;
  }
}
