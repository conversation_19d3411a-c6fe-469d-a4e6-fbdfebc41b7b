import React, { useState, useEffect } from 'react';
import { FaFilter, FaTimes, FaCheckCircle, FaClock, FaTimesCircle } from 'react-icons/fa';
import './MapFilters.css';
import { getTranslation } from '../../utils/translations';

const MapFilters = ({ 
  isVisible, 
  onClose, 
  onFiltersChange, 
  language = 'ar',
  parcelsData = []
}) => {
  const [filters, setFilters] = useState({
    salesStatus: [],
    priceRange: { min: 0, max: 1000000 },
    services: []
  });

  const [priceStats, setPriceStats] = useState({ min: 0, max: 1000000 });
  const [statusCounts, setStatusCounts] = useState({
    Available: 0,
    Reserved: 0,
    Sold: 0
  });
  const [availableServices, setAvailableServices] = useState([]);

  // Calculate statistics from parcels data
  useEffect(() => {
    if (parcelsData && parcelsData.length > 0) {
      // Calculate price range
      const prices = parcelsData
        .map(parcel => parseFloat(parcel.properties?.price_with_tax) || 0)
        .filter(price => price > 0);
      
      if (prices.length > 0) {
        const minPrice = Math.min(...prices);
        const maxPrice = Math.max(...prices);
        setPriceStats({ min: minPrice, max: maxPrice });
        setFilters(prev => ({
          ...prev,
          priceRange: { min: minPrice, max: maxPrice }
        }));
      }

      // Calculate status counts
      const counts = { Available: 0, Reserved: 0, Sold: 0 };
      parcelsData.forEach(parcel => {
        const status = parcel.properties?.sales_status;
        if (counts.hasOwnProperty(status)) {
          counts[status]++;
        }
      });
      setStatusCounts(counts);

      // Extract available services
      const services = new Set();
      parcelsData.forEach(parcel => {
        const parcelServices = parcel.properties?.services_available;
        if (parcelServices) {
          parcelServices.split(',').forEach(service => {
            services.add(service.trim());
          });
        }
      });
      setAvailableServices(Array.from(services).filter(service => service));
    }
  }, [parcelsData]);

  // Handle sales status filter change
  const handleStatusChange = (status) => {
    const newStatus = filters.salesStatus.includes(status)
      ? filters.salesStatus.filter(s => s !== status)
      : [...filters.salesStatus, status];
    
    const newFilters = { ...filters, salesStatus: newStatus };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Handle price range change
  const handlePriceChange = (type, value) => {
    const newPriceRange = { ...filters.priceRange, [type]: parseInt(value) };
    const newFilters = { ...filters, priceRange: newPriceRange };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Handle services filter change
  const handleServiceChange = (service) => {
    const newServices = filters.services.includes(service)
      ? filters.services.filter(s => s !== service)
      : [...filters.services, service];
    
    const newFilters = { ...filters, services: newServices };
    setFilters(newFilters);
    onFiltersChange(newFilters);
  };

  // Clear all filters
  const clearAllFilters = () => {
    const clearedFilters = {
      salesStatus: [],
      priceRange: { min: priceStats.min, max: priceStats.max },
      services: []
    };
    setFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  if (!isVisible) return null;

  return (
    <div className="map-filters-overlay">
      <div className="map-filters-container">
        <div className="filters-header">
          <h3 className="filters-title">
            <FaFilter className="filter-icon" />
            {language === 'ar' ? 'فلاتر الخريطة' : 'Map Filters'}
          </h3>
          <div className="filters-actions">
            <button
              className="clear-filters-btn"
              onClick={clearAllFilters}
            >
              {language === 'ar' ? 'مسح الكل' : 'Clear All'}
            </button>
            <button
              className="close-filters-btn"
              onClick={onClose}
            >
              <FaTimes />
            </button>
          </div>
        </div>

        <div className="filters-content">
          {/* Sales Status Filter */}
          <div className="filter-section">
            <h4 className="filter-section-title">
              {language === 'ar' ? 'حالة البيع' : 'Sales Status'}
            </h4>
            <div className="status-filters">
              <button
                className={`status-btn available ${filters.salesStatus.includes('Available') ? 'active' : ''}`}
                onClick={() => handleStatusChange('Available')}
              >
                <div className="status-icon">
                  <FaCheckCircle />
                </div>
                <span className="status-label">
                  {language === 'ar' ? 'متاح' : 'Available'}
                </span>
                <span className="status-count">{statusCounts.Available}</span>
              </button>
              
              <button
                className={`status-btn reserved ${filters.salesStatus.includes('Reserved') ? 'active' : ''}`}
                onClick={() => handleStatusChange('Reserved')}
              >
                <div className="status-icon">
                  <FaClock />
                </div>
                <span className="status-label">
                  {language === 'ar' ? 'محجوز' : 'Reserved'}
                </span>
                <span className="status-count">{statusCounts.Reserved}</span>
              </button>
              
              <button
                className={`status-btn sold ${filters.salesStatus.includes('Sold') ? 'active' : ''}`}
                onClick={() => handleStatusChange('Sold')}
              >
                <div className="status-icon">
                  <FaTimesCircle />
                </div>
                <span className="status-label">
                  {language === 'ar' ? 'مباع' : 'Sold'}
                </span>
                <span className="status-count">{statusCounts.Sold}</span>
              </button>
            </div>
          </div>

          {/* Price Range Filter */}
          <div className="filter-section">
            <h4 className="filter-section-title">
              {language === 'ar' ? 'نطاق السعر' : 'Price Range'}
            </h4>
            <div className="price-filter">
              <div className="price-inputs">
                <div className="price-input-group">
                  <label>{language === 'ar' ? 'من' : 'From'}</label>
                  <input
                    type="number"
                    value={filters.priceRange.min}
                    onChange={(e) => handlePriceChange('min', e.target.value)}
                    min={priceStats.min}
                    max={priceStats.max}
                  />
                </div>
                <div className="price-input-group">
                  <label>{language === 'ar' ? 'إلى' : 'To'}</label>
                  <input
                    type="number"
                    value={filters.priceRange.max}
                    onChange={(e) => handlePriceChange('max', e.target.value)}
                    min={priceStats.min}
                    max={priceStats.max}
                  />
                </div>
              </div>
              <div className="price-slider">
                <input
                  type="range"
                  min={priceStats.min}
                  max={priceStats.max}
                  value={filters.priceRange.min}
                  onChange={(e) => handlePriceChange('min', e.target.value)}
                  className="slider slider-min"
                />
                <input
                  type="range"
                  min={priceStats.min}
                  max={priceStats.max}
                  value={filters.priceRange.max}
                  onChange={(e) => handlePriceChange('max', e.target.value)}
                  className="slider slider-max"
                />
              </div>
            </div>
          </div>

          {/* Services Filter */}
          {availableServices.length > 0 && (
            <div className="filter-section">
              <h4 className="filter-section-title">
                {language === 'ar' ? 'الخدمات' : 'Services'}
              </h4>
              <div className="services-filter">
                {availableServices.map(service => (
                  <button
                    key={service}
                    className={`service-btn ${filters.services.includes(service) ? 'active' : ''}`}
                    onClick={() => handleServiceChange(service)}
                  >
                    {service}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MapFilters;
