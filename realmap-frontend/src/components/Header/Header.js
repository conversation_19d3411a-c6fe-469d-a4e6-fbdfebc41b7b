import React, { useState, useEffect } from "react";
import { FaSearch } from 'react-icons/fa';
import "./Header.css";

import { getProjects } from "../../services/api";
import { getTranslation } from "../../utils/translations";
import { useLanguage } from "../../App";

const Header = ({
  selectedProject,
  setSelectedProject,
  onParcelSearch,
  onToggleFilters,
  onStatusFiltersChange,
  statusFilters = [],
  totalUnitsCount = 0,
  onPriceFilterChange,
  priceFilter = { min: 0, max: 1000000 },
  onUsageFilterChange,
  usageFilters = [],
  isSharedView = false
}) => {
  const { language } = useLanguage();
  const [projects, setProjects] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [activeTab, setActiveTab] = useState(null); // 'search', 'status', 'price', 'usage'
  const [tempPriceFilter, setTempPriceFilter] = useState(priceFilter);

  useEffect(() => {
    const loadProjects = async () => {
      try {
        const response = await getProjects();
        setProjects(response.data.data);
      } catch (error) {
        console.error("Error loading projects:", error);
      }
    };

    loadProjects();
  }, []);

  // إغلاق القوائم المنسدلة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeTab && !event.target.closest('.header')) {
        setActiveTab(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeTab]);

  // تحديث tempPriceFilter عند تغيير priceFilter
  useEffect(() => {
    setTempPriceFilter(priceFilter);
  }, [priceFilter]);

  const selectedProjectData = projects.find(p => p.id === selectedProject);

  const handleSearchChange = (e) => {
    setSearchValue(e.target.value);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchValue.trim() && onParcelSearch) {
      onParcelSearch(searchValue.trim());
    }
  };

  const handleTabClick = (tab) => {
    if (activeTab === tab) {
      setActiveTab(null); // إغلاق التبويب إذا كان مفتوحاً
    } else {
      setActiveTab(tab); // فتح التبويب الجديد
    }
  };

  const handleStatusFilter = (status) => {
    console.log('Status filter clicked:', status);
    console.log('Current status filters:', statusFilters);

    if (onStatusFiltersChange) {
      const newFilters = statusFilters.includes(status)
        ? statusFilters.filter(s => s !== status)
        : [...statusFilters, status];

      console.log('New status filters:', newFilters);
      onStatusFiltersChange(newFilters);
    }
  };

  const handlePriceChange = (field, value) => {
    const newFilter = { ...tempPriceFilter, [field]: parseInt(value) || 0 };
    setTempPriceFilter(newFilter);
  };

  const applyPriceFilter = () => {
    if (onPriceFilterChange) {
      onPriceFilterChange(tempPriceFilter);
    }
  };

  const handleUsageFilter = (usage) => {
    if (onUsageFilterChange) {
      const newFilters = usageFilters.includes(usage)
        ? usageFilters.filter(u => u !== usage)
        : [...usageFilters, usage];
      onUsageFilterChange(newFilters);
    }
  };

  return (
    <header className="header">
      {/* الشريط الأساسي */}
      <div className="header-main">
        <div className="header-tabs">
          <button
            className={`header-tab search-tab ${activeTab === 'search' ? 'active' : ''}`}
            onClick={() => handleTabClick('search')}
          >
            <FaSearch className="search-tab-icon" />
            {language === "ar" ? "بحث..." : "Search..."}
          </button>
          <button
            className={`header-tab ${activeTab === 'status' ? 'active' : ''}`}
            onClick={() => handleTabClick('status')}
          >
            {language === "ar" ? "الحالة" : "Status"}
          </button>
          <button
            className={`header-tab ${activeTab === 'usage' ? 'active' : ''}`}
            onClick={() => handleTabClick('usage')}
          >
            {language === "ar" ? "الاستخدام" : "Usage"}
          </button>
          <button
            className={`header-tab ${activeTab === 'price' ? 'active' : ''}`}
            onClick={() => handleTabClick('price')}
          >
            {language === "ar" ? "السعر" : "Price"}
          </button>
        </div>
      </div>

      {/* منطقة المحتوى المنسدل */}
      {activeTab && (
        <div className="header-dropdown">
          {activeTab === 'search' && (
            <div className="search-dropdown">
              <form onSubmit={handleSearchSubmit} className="search-form">
                <input
                  type="text"
                  className="search-dropdown-input"
                  placeholder={
                    language === "ar"
                      ? "أدخل رقم الوحدة"
                      : "Enter unit number"
                  }
                  value={searchValue}
                  onChange={handleSearchChange}
                />
                <button type="submit" className="search-dropdown-btn">
                  {language === "ar" ? "ابحث" : "Search"}
                </button>
              </form>
            </div>
          )}

          {activeTab === 'status' && (
            <div className="status-dropdown">
              <div className="status-title">
                {language === "ar" ? "ألوان الخريطة" : "Map Colors"}
              </div>
              <div className="status-filters-grid">
                <button
                  className={`status-filter-btn all ${statusFilters.length === 0 ? 'active' : ''}`}
                  onClick={() => onStatusFiltersChange && onStatusFiltersChange([])}
                >
                  {language === "ar" ? "الكل" : "All"}
                </button>
                <button
                  className={`status-filter-btn sold ${statusFilters.includes('Sold') ? 'active' : ''}`}
                  onClick={() => handleStatusFilter('Sold')}
                >
                  {language === "ar" ? "مباع" : "Sold"}
                </button>
                <button
                  className={`status-filter-btn reserved ${statusFilters.includes('Reserved') ? 'active' : ''}`}
                  onClick={() => handleStatusFilter('Reserved')}
                >
                  {language === "ar" ? "محجوز" : "Reserved"}
                </button>
                <button
                  className={`status-filter-btn available ${statusFilters.includes('Available') ? 'active' : ''}`}
                  onClick={() => handleStatusFilter('Available')}
                >
                  {language === "ar" ? "متاح" : "Available"}
                </button>
              </div>
              <div className="status-count">
                {language === "ar" ? `${totalUnitsCount} وحدة` : `${totalUnitsCount} units`}
              </div>
            </div>
          )}

          {activeTab === 'usage' && (
            <div className="usage-dropdown">
              <div className="usage-title">
                {language === "ar" ? "نوع الاستخدام" : "Usage Type"}
              </div>
              <div className="usage-filters-grid">
                <button
                  className={`usage-filter-btn all ${usageFilters.length === 0 ? 'active' : ''}`}
                  onClick={() => onUsageFilterChange && onUsageFilterChange([])}
                >
                  {language === "ar" ? "الكل" : "All"}
                </button>
                <button
                  className={`usage-filter-btn residential ${usageFilters.includes('Residential') ? 'active' : ''}`}
                  onClick={() => handleUsageFilter('Residential')}
                >
                  {language === "ar" ? "سكني" : "Residential"}
                </button>
                <button
                  className={`usage-filter-btn commercial ${usageFilters.includes('Commercial') ? 'active' : ''}`}
                  onClick={() => handleUsageFilter('Commercial')}
                >
                  {language === "ar" ? "تجاري" : "Commercial"}
                </button>
                <button
                  className={`usage-filter-btn mixed ${usageFilters.includes('Mixed') ? 'active' : ''}`}
                  onClick={() => handleUsageFilter('Mixed')}
                >
                  {language === "ar" ? "مختلط" : "Mixed"}
                </button>
                <button
                  className={`usage-filter-btn industrial ${usageFilters.includes('Industrial') ? 'active' : ''}`}
                  onClick={() => handleUsageFilter('Industrial')}
                >
                  {language === "ar" ? "صناعي" : "Industrial"}
                </button>
              </div>
            </div>
          )}

          {activeTab === 'price' && (
            <div className="price-dropdown">
              <div className="price-title">
                {language === "ar" ? "نطاق السعر" : "Price Range"}
              </div>
              <div className="price-inputs-container">
                <div className="price-input-group">
                  <label>{language === "ar" ? "من" : "From"}</label>
                  <input
                    type="number"
                    className="price-input"
                    placeholder="0"
                    value={tempPriceFilter.min}
                    onChange={(e) => handlePriceChange('min', e.target.value)}
                  />
                </div>
                <div className="price-input-group">
                  <label>{language === "ar" ? "إلى" : "To"}</label>
                  <input
                    type="number"
                    className="price-input"
                    placeholder="1000000"
                    value={tempPriceFilter.max}
                    onChange={(e) => handlePriceChange('max', e.target.value)}
                  />
                </div>
              </div>
              <button className="apply-filter-btn" onClick={applyPriceFilter}>
                {language === "ar" ? "تطبيق" : "Apply"}
              </button>
            </div>
          )}
        </div>
      )}
    </header>
  );
};

export default Header;
