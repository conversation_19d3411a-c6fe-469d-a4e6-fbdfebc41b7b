import React from "react";
import { getTranslation } from "../../utils/translations";
import { APP_PATHS } from "../../config/constants";
import "./ProjectDetailsPanel.css";

const ProjectDetailsPanel = ({
  projectData,
  language,
  isVisible,
  onClose
}) => {
  if (!projectData || !isVisible) {
    return null;
  }

  const formatPrice = (price) => {
    if (!price) return "N/A";
    return new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(price);
  };

  const getAvailabilityPercentage = () => {
    const stats = projectData.statistics;
    if (!stats || stats.total_units === 0) return 0;
    return Math.round((stats.available_units / stats.total_units) * 100);
  };

  const handleVirtualTourClick = () => {
    if (projectData.virtual_tour_url) {
      window.open(projectData.virtual_tour_url, '_blank');
    } else {
      console.log("No virtual tour URL available for project:", projectData.name);
      // يمكن إضافة رسالة للمستخدم هنا
      alert(language === "ar" ? "لا يوجد جولة افتراضية متاحة لهذا المشروع" : "No virtual tour available for this project");
    }
  };

  const handleShareClick = () => {
    // Create shared URL with project parameters
    const baseUrl = projectData.share_url || window.location.origin + window.location.pathname;
    const shareUrl = `${baseUrl}?project=${projectData.id}&shared=true`;

    const shareText = language === "ar" ?
      `شاهد مشروع ${projectData.name}` :
      `Check out ${projectData.name} project`;

    if (navigator.share) {
      // استخدام Web Share API إذا كان متاحاً
      navigator.share({
        title: projectData.name,
        text: shareText,
        url: shareUrl,
      }).catch((error) => {
        console.log('Error sharing:', error);
        fallbackShare(shareUrl);
      });
    } else {
      fallbackShare(shareUrl);
    }
  };

  const fallbackShare = (url) => {
    // نسخ الرابط إلى الحافظة
    navigator.clipboard.writeText(url).then(() => {
      alert(language === "ar" ? "تم نسخ الرابط إلى الحافظة" : "Link copied to clipboard");
    }).catch(() => {
      // إذا فشل نسخ الرابط، افتح نافذة مشاركة
      window.open(`https://wa.me/?text=${encodeURIComponent(url)}`, '_blank');
    });
  };

  return (
    <div className={`project-details-panel ${isVisible ? "visible" : ""} ${language === "ar" ? "rtl" : "ltr"}`} dir={language === "ar" ? "rtl" : "ltr"}>
      {/* Close Button */}
      <button className="panel-close-btn" onClick={onClose}>
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>

      {/* ========= Header Section ========= */}
      <div className="card-header">
        <img
          src={projectData.default_unit_image_url || APP_PATHS.PUBLIC_ASSETS.LOGO_ONLY}
          alt={projectData.name}
          className="header-image"
          onError={(e) => {
            e.target.src = APP_PATHS.PUBLIC_ASSETS.LOGO_ONLY;
          }}
        />

        {/* Developer Logo
        <div className="logo">
          <div className="logo-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" strokeWidth="2" fill="none"/>
              <polyline points="9,22 9,12 15,12 15,22" stroke="currentColor" strokeWidth="2" fill="none"/>
            </svg>
          </div>
          <div className="logo-text">
            <span className="logo-text-ar">أصل</span>
            <span className="logo-text-en">ASSL</span>
          </div>
        </div> */}

        {/* Project Title */}
        <h1 className="card-title">{projectData.name}</h1>
      </div>

      {/* ========= Body / Info Section ========= */}
      <div className="card-body">
        <div className="info-section">
          <p className="description">
            {projectData.description ||
             (language === "ar" ? projectData.description_ar : projectData.description_en) ||
             (language === "ar" ? "أراضي سكنية واستثمارية في قلب الخرج" : "Residential and investment land in the heart of Al-Kharj")}
          </p>


          <div className="info-row">
            <div className="info-value">
              <span className="main-number">{projectData.statistics?.total_units || projectData.total_units || 1828}</span>
              {projectData.units_label ||
               (language === "ar" ? projectData.units_label_ar : projectData.units_label_en) ||
               (language === "ar" ? "وحدة" : "units")}
            </div>
            <div className="info-label green">
              {projectData.availability_label ||
               (language === "ar" ? projectData.availability_label_ar : projectData.availability_label_en) ||
               (language === "ar" ? "المتاح" : "Available")}
            </div>
          </div>

          <div className="info-row">
            <div className="info-value">
              {projectData.price_prefix ||
               (language === "ar" ? projectData.price_prefix_ar : projectData.price_prefix_en) ||
               (language === "ar" ? "يبدأ من" : "Starting from")}
              <span className="main-number">{formatPrice(projectData.statistics?.min_price || projectData.min_price || 200000)}</span>
              <img src="/map_controls/Saudi_Riyal_Symbol.png" alt="SAR" className="riyal-icon" />
            </div>
            <div className="info-label black">
              {projectData.price_label ||
               (language === "ar" ? projectData.price_label_ar : projectData.price_label_en) ||
               (language === "ar" ? "الأسعار" : "Prices")}
            </div>
          </div>

          <div className="info-row">
            <div className="info-value">
              {projectData.facilities ||
               projectData.facilities_ar ||
               projectData.facilities_en ||
               (language === "ar" ? "مساجد - مدارس - مواقف - حدائق" : "Mosques - Schools - Parking - Gardens")}
            </div>
            <div className="info-label black">
              {projectData.facilities_label ||
               (language === "ar" ? projectData.facilities_label_ar : projectData.facilities_label_en) ||
               (language === "ar" ? "المرافق" : "Facilities")}
            </div>
          </div>

          <div className="info-row">
            <div className="info-value">
              {projectData.owner ||
               projectData.owner_name ||
               projectData.developer_name ||
               (language === "ar" ? projectData.owner_ar : projectData.owner_en) ||
               (language === "ar" ? "منصة أصل العقارية" : "ASSL Real Estate Platform")}
            </div>
            <div className="info-label black">
              {projectData.owner_label ||
               (language === "ar" ? projectData.owner_label_ar : projectData.owner_label_en) ||
               (language === "ar" ? "المالك" : "Owner")}
            </div>
          </div>
        </div>

        <div className="side-icons">
          <div
            className={`icon-container-360 ${projectData.virtual_tour_url ? 'available' : 'disabled'}`}
            onClick={handleVirtualTourClick}
            title={projectData.virtual_tour_url ?
              (language === "ar" ? "جولة افتراضية 360°" : "360° Virtual Tour") :
              (language === "ar" ? "لا توجد جولة افتراضية متاحة" : "No virtual tour available")
            }
          >
            <img src="/360.png" alt="360" />
          </div>
          <div className="share-icon" onClick={handleShareClick}>
            <img src="/share white.png" alt="Share" />
          </div>
        </div>
      </div>

    </div>
  );
};

export default ProjectDetailsPanel;
