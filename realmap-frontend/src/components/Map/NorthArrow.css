.north-arrow-container {
  /* position: relative; */
  top: 0;
  right: 0;
  z-index: 1;
  /* background-color: rgba(31, 41, 55, 0.95); */
  border-radius: 8px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
  cursor: pointer;
  /* backdrop-filter: blur(10px); */
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.north-arrow-container:hover {
  background-color: rgba(31, 41, 55, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.3);
}

.north-arrow-container:active {
  transform: translateY(0);
}

.north-arrow-icon {
  transform-origin: center;
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.north-arrow-icon img {
  width: 40px;
  height: 48px;
  object-fit: contain;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .north-arrow-container {
    padding: 6px;
    min-width: 44px;
    border-radius: 6px;
  }

  .north-arrow-icon img {
    width: 32px;
    height: 38px;
  }
}

@media (max-width: 480px) {
  .north-arrow-container {
    padding: 4px;
    min-width: 40px;
    border-radius: 4px;
  }

  .north-arrow-icon img {
    width: 28px;
    height: 34px;
  }
}

@media (max-width: 375px) {
  .north-arrow-container {
    padding: 3px;
    min-width: 36px;
  }

  .north-arrow-icon img {
    width: 24px;
    height: 30px;
  }
}
