import React, { useEffect, useRef } from "react";
import "./ParcelPopover.css";
import { getTranslation } from "../../utils/translations";
import { useLanguage } from "../../App";
import ImagePager from "../ImagePager/ImagePager";

const ParcelPopover = ({
  parcelData,
  isVisible,
  onClose,
  position = { x: 0, y: 0 },
  mapContainer,
}) => {
  const popoverRef = useRef(null);
  const { language } = useLanguage();

  useEffect(() => {
    if (isVisible && popoverRef.current && mapContainer) {
      const popover = popoverRef.current;
      const containerRect = mapContainer.getBoundingClientRect();
      const popoverRect = popover.getBoundingClientRect();

      let x = position.x;
      let y = position.y;

      // Adjust position to keep popover within map bounds
      if (x + popoverRect.width > containerRect.width) {
        x = containerRect.width - popoverRect.width - 20;
      }
      if (x < 20) {
        x = 20;
      }
      if (y + popoverRect.height > containerRect.height) {
        y = position.y - popoverRect.height - 20;
      }
      if (y < 20) {
        y = 20;
      }

      popover.style.left = `${x}px`;
      popover.style.top = `${y}px`;
    }
  }, [isVisible, position, mapContainer]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (popoverRef.current && !popoverRef.current.contains(event.target)) {
        onClose();
      }
    };

    const handleEscape = (event) => {
      if (event.key === "Escape") {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener("mousedown", handleClickOutside);
      document.addEventListener("keydown", handleEscape);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isVisible, onClose]);

  if (!isVisible || !parcelData) {
    return null;
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return "#10b981"; // Emerald green
      case "reserved":
        return "#f59e0b"; // Amber
      case "sold":
        return "#ef4444"; // Red
      default:
        return "#6b7280"; // Gray
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return language === "ar" ? "متاح" : "Available";
      case "reserved":
        return language === "ar" ? "محجوز" : "Reserved";
      case "sold":
        return language === "ar" ? "مباع" : "Sold";
      default:
        return language === "ar" ? "غير محدد" : "Unknown";
    }
  };

  const getPropertyTypeText = (type) => {
    switch (type?.toLowerCase()) {
      case "residential":
        return language === "ar" ? "سكني" : "Residential";
      case "commercial":
        return language === "ar" ? "تجاري" : "Commercial";
      case "industrial":
        return language === "ar" ? "صناعي" : "Industrial";
      default:
        return type || "N/A";
    }
  };

  const getUsageTypeText = (type) => {
    switch (type?.toLowerCase()) {
      case "villa":
        return language === "ar" ? "فيلا" : "Villa";
      case "apartment":
        return language === "ar" ? "شقة" : "Apartment";
      case "office":
        return language === "ar" ? "مكتب" : "Office";
      case "shop":
        return language === "ar" ? "محل" : "Shop";
      default:
        return type || "N/A";
    }
  };

  const getPlotConditionText = (condition) => {
    switch (condition?.toLowerCase()) {
      case "flat":
        return language === "ar" ? "مستوي" : "Flat";
      case "sloped":
        return language === "ar" ? "منحدر" : "Sloped";
      case "corner":
        return language === "ar" ? "زاوية" : "Corner";
      default:
        return condition || "N/A";
    }
  };

  const getViewText = (view) => {
    switch (view?.toLowerCase()) {
      case "sea":
        return language === "ar" ? "بحر" : "Sea View";
      case "mountain":
        return language === "ar" ? "جبل" : "Mountain View";
      case "city":
        return language === "ar" ? "مدينة" : "City View";
      case "garden":
        return language === "ar" ? "حديقة" : "Garden View";
      default:
        return view || "N/A";
    }
  };

  const getInfrastructureText = (status) => {
    switch (status?.toLowerCase()) {
      case "complete":
        return language === "ar" ? "مكتملة" : "Complete";
      case "partial":
        return language === "ar" ? "جزئية" : "Partial";
      case "planned":
        return language === "ar" ? "مخططة" : "Planned";
      default:
        return status || "N/A";
    }
  };

  const formatPrice = (price) => {
    if (!price) return "N/A";
    return new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(
      price
    );
  };

  const formatArea = (area) => {
    if (!area) return "N/A";
    return new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(
      area
    );
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString(language === "ar" ? "ar-SA" : "en-US");
    } catch {
      return dateString;
    }
  };

  return (
    <div
      ref={popoverRef}
      className={`parcel-popover ${isVisible ? "visible" : ""}`}
    >
      <div className="popover-arrow top"></div>
      
      <div className="popover-header">
        <div className="popover-title">
          <h3>
            {getTranslation("parcel", language)} {parcelData.parcel_number}
          </h3>
          <div
            className="status-badge"
            style={{ backgroundColor: getStatusColor(parcelData.sales_status) }}
          >
            {getStatusText(parcelData.sales_status)}
          </div>
        </div>
        <button className="close-btn" onClick={onClose}>
          ✕
        </button>
      </div>

      <div className="popover-content">
        {parcelData.images && parcelData.images.length > 0 && (
          <div className="popover-image">
            <ImagePager
              images={parcelData.images}
              alt={`${getTranslation("parcel", language)} ${
                parcelData.parcel_number
              }`}
            />
          </div>
        )}

        <div className="details-grid">
          {/* Basic Information Section */}
          <div className="section-header">
            <h4>
              📋{" "}
              {language === "ar" ? "المعلومات الأساسية" : "Basic Information"}
            </h4>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("parcelNumber", language)}:
            </span>
            <span className="value">{parcelData.parcel_number}</span>
          </div>

          <div className="detail-item">
            <span className="label">{getTranslation("area", language)}:</span>
            <span className="value">{formatArea(parcelData.area)} m²</span>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("pricePerSqm", language)}:
            </span>
            <span className="value">
              {formatPrice(parcelData.price_per_sqm)} SAR
            </span>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("totalPrice", language)}:
            </span>
            <span className="value">
              {formatPrice(parcelData.total_price)} SAR
            </span>
          </div>

          {/* Property Details Section */}
          <div className="section-header">
            <h4>
              🏠 {language === "ar" ? "تفاصيل العقار" : "Property Details"}
            </h4>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("propertyType", language)}:
            </span>
            <span className="value">
              {getPropertyTypeText(parcelData.property_type)}
            </span>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("usageType", language)}:
            </span>
            <span className="value">
              {getUsageTypeText(parcelData.usage_type)}
            </span>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("plotCondition", language)}:
            </span>
            <span className="value">
              {getPlotConditionText(parcelData.plot_condition)}
            </span>
          </div>

          <div className="detail-item">
            <span className="label">{getTranslation("view", language)}:</span>
            <span className="value">{getViewText(parcelData.view)}</span>
          </div>

          {/* Financial Information Section */}
          <div className="section-header">
            <h4>
              💰 {language === "ar" ? "المعلومات المالية" : "Financial Info"}
            </h4>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("expectedROI", language)}:
            </span>
            <span className="value">{parcelData.expected_roi || "N/A"}%</span>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("deliveryDate", language)}:
            </span>
            <span className="value">
              {formatDate(parcelData.delivery_date)}
            </span>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("downPayment", language)}:
            </span>
            <span className="value">
              {formatPrice(parcelData.down_payment)} SAR
            </span>
          </div>

          <div className="detail-item full-width">
            <span className="label">
              {getTranslation("paymentTerms", language)}:
            </span>
            <span className="value">
              {parcelData.payment_terms || "N/A"}
            </span>
          </div>

          {/* Infrastructure Section */}
          <div className="section-header">
            <h4>
              🏗️ {language === "ar" ? "البنية التحتية" : "Infrastructure"}
            </h4>
          </div>

          <div className="detail-item">
            <span className="label">
              {getTranslation("infrastructure", language)}:
            </span>
            <span className="value">
              {getInfrastructureText(parcelData.infrastructure_status)}
            </span>
          </div>

          <div className="detail-item full-width">
            <span className="label">
              {getTranslation("services", language)}:
            </span>
            <span className="value">
              {parcelData.services_available || "N/A"}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParcelPopover;
