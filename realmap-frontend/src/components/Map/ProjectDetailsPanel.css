.project-details-panel {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(100%);
  width: 90%;
  max-width: 500px;
  background: rgba(30, 30, 30, 0.85);
  backdrop-filter: blur(5px);
  border-radius: 20px 20px 0 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  max-height: 70vh;
  overflow-y: auto;
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0;
  font-family: 'Cairo', 'Tajawal', sans-serif;
  color: #ffffff;
}

.project-details-panel.visible {
  transform: translateX(-50%) translateY(0);
}

/* Close Button */
.panel-close-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.2s ease;
  z-index: 10;
}

.panel-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

/* ========= Header Section Styles ========= */
.card-header {
  position: relative;
  text-align: center;
}

.header-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 20px 20px 0 0;
}

.logo {
  position: absolute;
  top: 15px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.logo-icon {
  color: white;
}

.logo-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  color: white;
}

.logo-text-ar {
  font-size: 24px;
  font-weight: 700;
  line-height: 1;
}

.logo-text-en {
  font-size: 14px;
  font-weight: 500;
  letter-spacing: 2px;
  line-height: 1;
}

.card-title {
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 52px;
  font-weight: 800;
  color: white;
  width: 100%;
  padding: 0 10px;
  margin: 0;
  text-shadow: 3px 3px 8px rgba(0, 0, 0, 0.8);
}

/* ========= Body/Info Section Styles ========= */
.card-body {
  display: flex;
  padding: 15px;
  position: relative;
}

.side-icons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding-right: 15px;
}

.icon-container-360 {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  width: 50px;
  height: 50px;
}

.icon-container-360:hover {
  transform: scale(1.1);
}

.icon-container-360.available {
  opacity: 1;
}

.icon-container-360.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon-container-360.disabled:hover {
  transform: none;
}

.icon-container-360 img {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.share-icon {
  cursor: pointer;
  transition: transform 0.2s ease;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.share-icon:hover {
  transform: scale(1.1);
}

.share-icon img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.info-section {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.description {
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 10px 0;
  color: white;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 5px;
}

.info-label {
  padding: 6px 20px;
  border-radius: 15px;
  font-size: 16px;
  font-weight: 700;
  color: white;
  flex-shrink: 0;
}

.info-label.green {
  background-color: #43c193;
}

.info-label.black {
  background-color: #1a1a1a;
  border: 1px solid #444;
}

.info-value {
  font-size: 18px;
  font-weight: 500;
  color: white;
}

.info-value .main-number {
  font-size: 24px;
  font-weight: 700;
  margin: 0 4px;
}

.riyal-icon {
  width: 20px;
  height: 20px;
  object-fit: contain;
  margin-left: 4px;
  vertical-align: middle;
}



/* Mobile Responsive */
@media (max-width: 768px) {
  .project-details-panel {
    left: 0;
    right: 0;
    width: 100%;
    max-width: none;
    transform: translateY(100%);
    padding: 0;
    max-height: 70vh;
    border-radius: 16px 16px 0 0;
  }

  .project-details-panel.visible {
    transform: translateY(0);
  }

  .card-title {
    font-size: 36px;
  }

  .card-body {
    padding: 12px;
  }

  .side-icons {
    gap: 12px;
    padding-right: 12px;
  }

  .icon-container-360 {
    width: 45px;
    height: 45px;
  }

  .icon-container-360 img {
    width: 35px;
    height: 35px;
  }

  .share-icon {
    width: 45px;
    height: 45px;
  }

  .share-icon img {
    width: 28px;
    height: 28px;
  }

  .description {
    font-size: 18px;
  }

  .info-value {
    font-size: 16px;
  }

  .info-value .main-number {
    font-size: 20px;
  }

  .info-label {
    font-size: 14px;
    padding: 5px 16px;
  }

  .riyal-icon {
    width: 16px;
    height: 16px;
  }
}



/* RTL Support */
.project-details-panel.rtl .panel-close-btn {
  right: auto;
  left: 16px;
}

.project-details-panel.rtl .logo {
  left: auto;
  right: 20px;
}

.project-details-panel.rtl .side-icons {
  padding-right: 0;
  padding-left: 15px;
}

.project-details-panel.rtl .info-row {
  flex-direction: row-reverse;
}

.project-details-panel.rtl .riyal-icon {
  margin-left: 0;
  margin-right: 4px;
}
