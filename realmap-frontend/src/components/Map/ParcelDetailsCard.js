import React, { useState, useEffect } from "react";
import "./ParcelDetailsCard.css";
import { getTranslation } from "../../utils/translations";
import { useLanguage } from "../../App";
import ImagePager from "../ImagePager/ImagePager";

const ParcelDetailsCard = ({ parcelData, onClose, isVisible }) => {
  const { language } = useLanguage();
  const [dragY, setDragY] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [activeTab, setActiveTab] = useState(0); // 0 for basic info, 1 for property details

  // Handle touch events for mobile drag-to-close
  const handleTouchStart = (e) => {
    setIsDragging(true);
    setDragY(e.touches[0].clientY);
  };

  const handleTouchMove = (e) => {
    if (!isDragging) return;
    const currentY = e.touches[0].clientY;
    const deltaY = currentY - dragY;

    if (deltaY > 0) {
      // Only allow dragging down
      e.currentTarget.style.transform = `translateY(${deltaY}px)`;
    }
  };

  const handleTouchEnd = (e) => {
    if (!isDragging) return;
    setIsDragging(false);

    const currentY = e.changedTouches[0].clientY;
    const deltaY = currentY - dragY;

    if (deltaY > 100) {
      // If dragged down more than 100px, close
      onClose();
    } else {
      // Reset position
      e.currentTarget.style.transform = "translateY(0)";
    }
  };

  if (!isVisible || !parcelData) {
    return null;
  }

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return "#10b981"; // Emerald green
      case "reserved":
        return "#f59e0b"; // Amber
      case "sold":
        return "#ef4444"; // Red
      default:
        return "#6b7280"; // Gray
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return getTranslation("available", language);
      case "reserved":
        return getTranslation("reserved", language);
      case "sold":
        return getTranslation("sold", language);
      default:
        return status || "N/A";
    }
  };

  const getUsageTypeText = (type) => {
    switch (type?.toLowerCase()) {
      case "residential":
        return getTranslation("residential", language);
      case "commercial":
        return getTranslation("commercial", language);
      case "industrial":
        return getTranslation("industrial", language);
      case "villa":
        return getTranslation("villa", language);
      case "apartment":
        return getTranslation("apartment", language);
      default:
        return type || "N/A";
    }
  };

  const getPropertyTypeText = (type) => {
    switch (type?.toLowerCase()) {
      case "land":
        return getTranslation("land", language);
      case "villa":
        return getTranslation("villa", language);
      case "apartment":
        return getTranslation("apartment", language);
      case "residential":
        return getTranslation("residential", language);
      case "commercial":
        return getTranslation("commercial", language);
      default:
        return type || "N/A";
    }
  };

  const getPlotConditionText = (condition) => {
    switch (condition?.toLowerCase()) {
      case "fenced":
        return getTranslation("fenced", language);
      case "ready":
        return getTranslation("ready", language);
      case "under development":
      case "underdevelopment":
        return getTranslation("underDevelopment", language);
      default:
        return condition || "N/A";
    }
  };

  const getViewText = (view) => {
    switch (view?.toLowerCase()) {
      case "main road":
      case "mainroad":
        return getTranslation("mainRoad", language);
      case "garden":
        return getTranslation("garden", language);
      case "mountain":
        return getTranslation("mountain", language);
      case "sea":
        return getTranslation("sea", language);
      case "internal street":
      case "internalstreet":
        return getTranslation("internalStreet", language);
      default:
        return view || "N/A";
    }
  };

  const formatArea = (area) => {
    if (!area) return "N/A";
    return new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(
      area
    );
  };

  return (
    <div
      className={`parcel-details-card ${isVisible ? "visible" : ""}`}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      <div className="card-header">
        <div className="parcel-title">
          <h3>
            {getTranslation("parcel", language)} {parcelData.parcel_number}
          </h3>
          <div
            className="status-badge"
            style={{ backgroundColor: getStatusColor(parcelData.sales_status) }}
          >
            {getStatusText(parcelData.sales_status)}
          </div>
        </div>
        <button className="close-btn" onClick={onClose}>
          ✕
        </button>
      </div>

      {/* Tabs Navigation */}
      <div className="tabs-navigation">
        <button
          className={`tab-button ${activeTab === 0 ? "active" : ""}`}
          onClick={() => setActiveTab(0)}
          title={language === "ar" ? "المعلومات الأساسية" : "Basic Information"}
        >
          📋
        </button>
        <button
          className={`tab-button ${activeTab === 1 ? "active" : ""}`}
          onClick={() => setActiveTab(1)}
          title={language === "ar" ? "تفاصيل العقار" : "Property Details"}
        >
          🏠
        </button>
      </div>

      <div className="card-content">
        {/* Tab Content */}
        {activeTab === 0 && (
          <div className="tab-content">
            <div className="details-grid">
              <div className="detail-item">
                <span className="label">
                  {getTranslation("parcelNumber", language)}:
                </span>
                <span className="value">{parcelData.parcel_number}</span>
              </div>

              <div className="detail-item">
                <span className="label">{getTranslation("area", language)}:</span>
                <span className="value">{formatArea(parcelData.area)} m²</span>
              </div>

              <div className="detail-item">
                <span className="label">
                  {getTranslation("propertyType", language)}:
                </span>
                <span className="value">
                  {getPropertyTypeText(parcelData.property_type)}
                </span>
              </div>

              <div className="detail-item">
                <span className="label">
                  {getTranslation("usageType", language)}:
                </span>
                <span className="value">
                  {getUsageTypeText(parcelData.usage_type)}
                </span>
              </div>

              <div className="detail-item">
                <span className="label">
                  {getTranslation("plotCondition", language)}:
                </span>
                <span className="value">
                  {getPlotConditionText(parcelData.plot_condition)}
                </span>
              </div>

              <div className="detail-item">
                <span className="label">{getTranslation("view", language)}:</span>
                <span className="value">{getViewText(parcelData.view)}</span>
              </div>
            </div>
          </div>
        )}

        {activeTab === 1 && (
          <div className="tab-content">
            <div className="card-image">
              <ImagePager
                images={parcelData.images || []}
                alt={`${getTranslation("parcel", language)} ${
                  parcelData.parcel_number
                }`}
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ParcelDetailsCard;
