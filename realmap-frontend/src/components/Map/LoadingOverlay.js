import React from "react";

const LoadingOverlay = ({ isVisible, progress, message }) => {
  if (!isVisible) return null;

  return (
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.7)",
        display: "flex",
        flexDirection: "column",
        justifyContent: "center",
        alignItems: "center",
        zIndex: 1000,
        color: "white",
        fontFamily: "Arial, sans-serif",
      }}
    >
      {/* Loading Spinner with Logo */}
      <div
        style={{
          position: "relative",
          width: "60px",
          height: "60px",
          marginBottom: "20px",
        }}
      >
        {/* Spinning Circle */}
        <div
          style={{
            width: "60px",
            height: "60px",
            border: "4px solid rgba(255, 255, 255, 0.3)",
            borderTop: "4px solid #007cbf",
            borderRadius: "50%",
            animation: "spin 1s linear infinite",
          }}
        />

        {/* Logo in Center */}
        <img
          src="/logo_only.png"
          alt="Logo"
          style={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            width: "30px",
            height: "30px",
            objectFit: "contain",
            zIndex: 1,
          }}
        />
      </div>
      
      {/* Loading Message */}
      <div
        style={{
          fontSize: "18px",
          fontWeight: "bold",
          marginBottom: "15px",
          textAlign: "center",
          maxWidth: "300px",
        }}
      >
        {message}
      </div>
      
      {/* Progress Bar */}
      <div
        style={{
          width: "300px",
          height: "8px",
          backgroundColor: "rgba(255, 255, 255, 0.3)",
          borderRadius: "4px",
          overflow: "hidden",
          marginBottom: "10px",
        }}
      >
        <div
          style={{
            width: `${progress}%`,
            height: "100%",
            backgroundColor: "#007cbf",
            borderRadius: "4px",
            transition: "width 0.3s ease",
          }}
        />
      </div>
      
      {/* Progress Percentage */}
      <div
        style={{
          fontSize: "14px",
          opacity: 0.8,
        }}
      >
        {Math.round(progress)}%
      </div>
      
      {/* CSS Animation for Spinner */}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default LoadingOverlay;
