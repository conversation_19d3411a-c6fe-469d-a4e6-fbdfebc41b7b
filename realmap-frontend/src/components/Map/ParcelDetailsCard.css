.parcel-details-card {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1000;
  max-height: 50vh;
  overflow: hidden;
}

.parcel-details-card.visible {
  transform: translateY(0);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e9ecef;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.card-header:hover::before {
  left: 100%;
}

.parcel-title {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.parcel-title h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Tabs Navigation */
.tabs-navigation {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  padding: 0;
}

.tab-button {
  flex: 1;
  background: transparent;
  border: none;
  padding: 16px 20px;
  font-size: 20px;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  color: #6b7280;
  border-bottom: 3px solid transparent;
}

.tab-button:hover {
  background: rgba(31, 41, 55, 0.05);
  color: #1f2937;
}

.tab-button.active {
  color: #1f2937;
  background: white;
  border-bottom-color: #1f2937;
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 2px 2px 0 0;
}

/* Tab Content */
.tab-content {
  width: 100%;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-content {
  padding: 20px 24px;
  overflow-y: auto;
  max-height: calc(50vh - 140px); /* Adjusted for tabs */
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  width: 100%;
}

/* Custom scrollbar */
.details-grid::-webkit-scrollbar {
  width: 6px;
}

.details-grid::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.details-grid::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.details-grid::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  border-left: 3px solid #1f2937;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-item .label {
  font-size: 13px;
  color: #64748b;
  font-weight: 600;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item .value {
  font-size: 16px;
  color: #1e293b;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  word-wrap: break-word;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item.full-width .value {
  font-size: 14px;
  line-height: 1.4;
}

.section-header {
  grid-column: 1 / -1;
  margin: 20px 0 12px 0;
  padding: 12px 16px;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 8px;
  color: white;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: white;
  display: flex;
  align-items: center;
  gap: 10px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section-header:first-child {
  margin-top: 0;
}

.card-image {
  width: 100%;
  height: 250px;
  border-radius: 12px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .parcel-details-card {
    max-height: 60vh;
  }

  .card-header {
    padding: 16px 20px 12px;
  }

  .parcel-title h3 {
    font-size: 18px;
  }

  .card-content {
    padding: 16px 20px;
    max-height: calc(60vh - 200px); /* Adjusted for tabs */
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .card-image {
    width: 100%;
    height: 200px;
  }

  .tab-button {
    padding: 12px 16px;
    font-size: 18px;
  }

  .detail-item .label {
    font-size: 13px;
  }

  .detail-item .value {
    font-size: 15px;
  }

  .section-header {
    margin: 12px 0 6px 0;
  }

  .section-header h4 {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .card-header {
    padding: 12px 16px 8px;
  }

  .parcel-title h3 {
    font-size: 16px;
  }

  .status-badge {
    padding: 3px 8px;
    font-size: 11px;
  }

  .card-content {
    padding: 12px 16px;
  }

  .card-image {
    height: 150px;
  }

  .tab-button {
    padding: 10px 12px;
    font-size: 16px;
  }
}

/* RTL Support */
.rtl .parcel-details-card {
  direction: rtl;
}

.rtl .parcel-title {
  flex-direction: row-reverse;
}

.rtl .card-header {
  flex-direction: row-reverse;
}

.rtl .card-content {
  direction: rtl;
}

.rtl .tabs-navigation {
  direction: rtl;
}

.rtl .tab-button {
  direction: rtl;
}

/* Animation for card appearance */
@keyframes slideUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.parcel-details-card.visible {
  animation: slideUp 0.3s ease-out;
}

/* Backdrop blur effect */
.parcel-details-card::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(2px);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.parcel-details-card.visible::before {
  opacity: 1;
}

/* Drag handle for mobile */
.card-header::after {
  content: "";
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 5px;
  background: rgba(255, 255, 255, 0.4);
  border-radius: 3px;
  z-index: 2;
}

@media (min-width: 769px) {
  .card-header::after {
    display: none;
  }
}
