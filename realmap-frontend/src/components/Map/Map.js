import React, { useEffect, useRef, useState } from "react";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import "./Map.css";
import {
  getBlocksGeoJson,
  getParcelsGeoJson,
  getProjectMapLayers,
} from "../../services/api";
import { MAPBOX_CONFIG } from "../../config/constants";

mapboxgl.accessToken = MAPBOX_CONFIG.ACCESS_TOKEN;

const Map = ({
  selectedProject,
  selectedBlock,
  setSelectedBlock,
  selectedParcel,
  setSelectedParcel,
}) => {
  const mapContainer = useRef(null);
  const map = useRef(null);
  const [lng, setLng] = useState(39.97832);
  const [lat, setLat] = useState(21.46557);
  const [zoom, setZoom] = useState(15);
  const [mapLayers, setMapLayers] = useState([]);
  const [blocksData, setBlocksData] = useState(null);
  const [parcelsData, setParcelsData] = useState(null);

  // Initialize map
  useEffect(() => {
    if (map.current) return; // Initialize map only once

    console.log("Initializing map...");
    console.log("Map container:", mapContainer.current);

    try {
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: "mapbox://styles/mapbox/satellite-v9",
        center: [39.9778, 21.4667],
        zoom: 7,
      });

      console.log("Map initialized successfully");

      // Add load event listener
      map.current.on("load", () => {
        console.log("Map loaded successfully");
        loadMapLayers();
      });

      // Add error event listener
      map.current.on("error", (e) => {
        console.error("Map error:", e);
      });
    } catch (error) {
      console.error("Error initializing map:", error);
    }

    map.current.on("move", () => {
      setLng(map.current.getCenter().lng.toFixed(5));
      setLat(map.current.getCenter().lat.toFixed(5));
      setZoom(map.current.getZoom().toFixed(2));
    });

    // Add navigation control
    map.current.addControl(new mapboxgl.NavigationControl(), "top-right");

    // Add scale control
    map.current.addControl(new mapboxgl.ScaleControl(), "bottom-right");

    // Clean up on unmount
    return () => map.current.remove();
  }, []);

  // Load project map layers
  useEffect(() => {
    if (!map.current || !selectedProject) return;

    const loadMapLayers = async () => {
      try {
        const response = await getProjectMapLayers(selectedProject);
        setMapLayers(response.data.data);
      } catch (error) {
        console.error("Error loading map layers:", error);
      }
    };

    loadMapLayers();
  }, [selectedProject]);

  // Add map layers
  useEffect(() => {
    if (!map.current || !mapLayers.length) return;

    // Wait for map to be loaded
    if (!map.current.loaded()) {
      map.current.on("load", addLayers);
    } else {
      addLayers();
    }

    function addLayers() {
      // Remove existing layers
      if (map.current.getSource("background")) {
        map.current.removeLayer("background-layer");
        map.current.removeSource("background");
      }

      // // Add image layers
      // mapLayers.forEach((layer) => {
      //   if (layer.type === "image" && layer.is_active) {
      //     const coordinates = JSON.parse(layer.coordinates);

      //     map.current.addSource(layer.name, {
      //       type: "image",
      //       url: `http://localhost:8001/storage/${layer.file_path}`,
      //       coordinates: coordinates,
      //     });

      //     map.current.addLayer({
      //       id: `${layer.name}-layer`,
      //       type: "raster",
      //       source: layer.name,
      //       paint: {
      //         "raster-opacity": layer.opacity,
      //       },
      //       minzoom: layer.min_zoom || 0,
      //       maxzoom: layer.max_zoom || 22,
      //     });
      //   }
      // });
    }

    return () => {
      // Clean up layers when component unmounts or when layers change
      mapLayers.forEach((layer) => {
        if (map.current.getLayer(`${layer.name}-layer`)) {
          map.current.removeLayer(`${layer.name}-layer`);
        }
        if (map.current.getSource(layer.name)) {
          map.current.removeSource(layer.name);
        }
      });
    };
  }, [mapLayers]);

  // Load blocks data
  useEffect(() => {
    if (!map.current || !selectedProject) return;

    const loadBlocksData = async () => {
      try {
        const response = await getBlocksGeoJson(selectedProject);
        setBlocksData(response.data);
      } catch (error) {
        console.error("Error loading blocks data:", error);
      }
    };

    loadBlocksData();
  }, [selectedProject]);

  // Add blocks layer
  useEffect(() => {
    if (!map.current || !blocksData) return;

    // Wait for map to be loaded
    if (!map.current.loaded()) {
      map.current.on("load", addBlocksLayer);
    } else {
      addBlocksLayer();
    }

    function addBlocksLayer() {
      // Remove existing layers
      if (map.current.getSource("blocks")) {
        map.current.removeLayer("blocks-fill");
        map.current.removeLayer("blocks-outline");
        map.current.removeLayer("blocks-label");
        map.current.removeSource("blocks");
      }

      // Add blocks source
      map.current.addSource("blocks", {
        type: "geojson",
        data: blocksData,
      });

      // Add blocks fill layer
      map.current.addLayer({
        id: "blocks-fill",
        type: "fill",
        source: "blocks",
        paint: {
          "fill-color": [
            "case",
            ["==", ["get", "ID"], selectedBlock ? parseInt(selectedBlock) : -1],
            "#ff9900",
            "rgba(0, 0, 255, 0.1)",
          ],
          "fill-opacity": 0.5,
        },
      });

      // Add blocks outline layer
      map.current.addLayer({
        id: "blocks-outline",
        type: "line",
        source: "blocks",
        paint: {
          "line-color": "#0000ff",
          "line-width": [
            "case",
            ["==", ["get", "ID"], selectedBlock ? parseInt(selectedBlock) : -1],
            3,
            1,
          ],
        },
      });

      // Add blocks label layer
      map.current.addLayer({
        id: "blocks-label",
        type: "symbol",
        source: "blocks",
        layout: {
          "text-field": ["get", "BlockNo"],
          "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
          "text-size": 12,
          "text-offset": [0, 0],
          "text-anchor": "center",
        },
        paint: {
          "text-color": "#000000",
          "text-halo-color": "#ffffff",
          "text-halo-width": 1,
        },
      });

      // Add click event
      map.current.on("click", "blocks-fill", (e) => {
        if (e.features.length > 0) {
          const blockId = e.features[0].properties.ID;
          setSelectedBlock(blockId);
        }
      });

      // Change cursor on hover
      map.current.on("mouseenter", "blocks-fill", () => {
        map.current.getCanvas().style.cursor = "pointer";
      });

      map.current.on("mouseleave", "blocks-fill", () => {
        map.current.getCanvas().style.cursor = "";
      });
    }

    return () => {
      // Clean up event listeners
      if (map.current) {
        map.current.off("click", "blocks-fill");
        map.current.off("mouseenter", "blocks-fill");
        map.current.off("mouseleave", "blocks-fill");
      }
    };
  }, [blocksData, selectedBlock, setSelectedBlock]);

  // Load parcels data
  useEffect(() => {
    if (!map.current || !selectedBlock) return;

    const loadParcelsData = async () => {
      try {
        const response = await getParcelsGeoJson(selectedBlock);
        setParcelsData(response.data);
      } catch (error) {
        console.error("Error loading parcels data:", error);
      }
    };

    loadParcelsData();
  }, [selectedBlock]);

  // Add parcels layer
  useEffect(() => {
    if (!map.current || !parcelsData) return;

    // Wait for map to be loaded
    if (!map.current.loaded()) {
      map.current.on("load", addParcelsLayer);
    } else {
      addParcelsLayer();
    }

    function addParcelsLayer() {
      // Remove existing layers
      if (map.current.getSource("parcels")) {
        map.current.removeLayer("parcels-fill");
        map.current.removeLayer("parcels-outline");
        map.current.removeLayer("parcels-label");
        map.current.removeSource("parcels");
      }

      // Add parcels source
      map.current.addSource("parcels", {
        type: "geojson",
        data: parcelsData,
      });

      // Add parcels fill layer
      map.current.addLayer({
        id: "parcels-fill",
        type: "fill",
        source: "parcels",
        paint: {
          "fill-color": [
            "match",
            ["get", "sales_status"],
            "Available",
            "rgba(0, 255, 0, 0.5)",
            "Reserved",
            "rgba(255, 255, 0, 0.5)",
            "Sold",
            "rgba(255, 0, 0, 0.5)",
            "rgba(128, 128, 128, 0.5)",
          ],
          "fill-opacity": [
            "case",
            ["==", ["get", "Parcel_No"], selectedParcel || ""],
            0.9,
            0.5,
          ],
        },
      });

      // Add parcels outline layer
      map.current.addLayer({
        id: "parcels-outline",
        type: "line",
        source: "parcels",
        paint: {
          "line-color": [
            "case",
            ["==", ["get", "Parcel_No"], selectedParcel || ""],
            "#000000",
            "#ffffff",
          ],
          "line-width": [
            "case",
            ["==", ["get", "Parcel_No"], selectedParcel || ""],
            2,
            0.5,
          ],
        },
      });

      // Add parcels label layer
      map.current.addLayer({
        id: "parcels-label",
        type: "symbol",
        source: "parcels",
        layout: {
          "text-field": ["get", "Parcel_No"],
          "text-font": ["Open Sans Regular", "Arial Unicode MS Regular"],
          "text-size": 10,
          "text-offset": [0, 0],
          "text-anchor": "center",
        },
        paint: {
          "text-color": "#000000",
          "text-halo-color": "#ffffff",
          "text-halo-width": 1,
        },
      });

      // Add click event
      map.current.on("click", "parcels-fill", (e) => {
        if (e.features.length > 0) {
          const parcelNo = e.features[0].properties.Parcel_No;
          setSelectedParcel(parcelNo);
        }
      });

      // Change cursor on hover
      map.current.on("mouseenter", "parcels-fill", () => {
        map.current.getCanvas().style.cursor = "pointer";
      });

      map.current.on("mouseleave", "parcels-fill", () => {
        map.current.getCanvas().style.cursor = "";
      });
    }

    return () => {
      // Clean up event listeners
      if (map.current) {
        map.current.off("click", "parcels-fill");
        map.current.off("mouseenter", "parcels-fill");
        map.current.off("mouseleave", "parcels-fill");
      }
    };
  }, [parcelsData, selectedParcel, setSelectedParcel]);

  return (
    <div className="map-container">
      <div className="map-info">
        Longitude: {lng} | Latitude: {lat} | Zoom: {zoom}
      </div>
      <div ref={mapContainer} className="map" />
    </div>
  );
};

export default Map;
