import React, { useEffect, useRef, useState, useCallback } from "react";
import ReactDOM from "react-dom";
import mapboxgl from "mapbox-gl";
import "mapbox-gl/dist/mapbox-gl.css";
import {
  getProjects,
  getProject,
  getBlocks,
  getParcels,
  getProjectMapLayers,
  searchParcels,
  getProjectLandmarks
} from "../../services/api";
import LoadingOverlay from "./LoadingOverlay";
import MapControls from "./MapControls";
import ParcelPopupContent from "./ParcelPopupContent";
import RealMapLogo from "./RealMapLogo";
import RightControls from "./RightControls";
import MapFilters from "../Filters/MapFilters";
import TermsNotification from "../Terms/TermsNotification";
import ProjectDetailsPanel from "./ProjectDetailsPanel";
import { getTranslation } from "../../utils/translations";
import { MAPBOX_CONFIG, APP_PATHS, getMapLayerFileUrl, getStorageUrl } from "../../config/constants";

mapboxgl.accessToken = MAPBOX_CONFIG.ACCESS_TOKEN;

const OptimizedMap = ({
  selectedProject,
  onProjectChange,
  mapStyle = "streets-v11",
  onMapStyleChange,
  onLanguageToggle,
  language = "ar",
  onParcelSearch,
  onMapReady,
  filtersVisible = false,
  onToggleFilters,
  filters = null,
  statusFilters = [],
  priceFilter = { min: 0, max: 1000000 },
  usageFilters = [],
  isSharedView = false,
  sharedProjectId = null
}) => {
  const mapContainerRef = useRef(null);
  const mapRef = useRef(null);
  const popupRef = useRef(null);
  const [projects, setProjects] = useState([]);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [selectedBlockId, setSelectedBlockId] = useState(null);
  const [selectedParcelId, setSelectedParcelId] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingProgress, setLoadingProgress] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState("");
  const [projectMarkers, setProjectMarkers] = useState([]);
  const [mapLayers, setMapLayers] = useState([]);
  const [selectedParcelData, setSelectedParcelData] = useState(null);
  const [isParcelPopoverVisible, setIsParcelPopoverVisible] = useState(false);
  const [popoverPosition, setPopoverPosition] = useState({ x: 0, y: 0 });
  const [allParcelsData, setAllParcelsData] = useState([]);
  const [activeFilters, setActiveFilters] = useState({
    salesStatus: [],
    priceRange: { min: 0, max: 1000000 },
    services: []
  });

  // Project details panel state
  const [selectedProjectData, setSelectedProjectData] = useState(null);
  const [isProjectPanelVisible, setIsProjectPanelVisible] = useState(false);

  // Project panel handlers
  const handleCloseProjectPanel = () => {
    setIsProjectPanelVisible(false);
    setSelectedProjectData(null);
  };

  // Function to manage price display layer
  const managePriceDisplayLayer = (isPriceFilterActive, isStatusFilterActive, isUsageFilterActive) => {
    if (!mapRef.current || !mapRef.current.isStyleLoaded()) return;

    const priceLayerId = "parcels-prices";

    try {
      // Check if price layer exists
      const layerExists = mapRef.current.getLayer(priceLayerId);

      if (isPriceFilterActive && !isStatusFilterActive && !isUsageFilterActive) {
        // Show prices when only price filter is active
        if (!layerExists) {
          // Create price display layer
          mapRef.current.addLayer({
            id: priceLayerId,
            type: "symbol",
            source: "parcels",
            layout: {
              "text-field": [
                "format",
                ["to-string", ["get", "price_with_tax"]],
                {},
                " ر.س",
                {}
              ],
              "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
              "text-size": 10,
              "text-offset": [0, -1.5],
              "text-anchor": "center",
              "text-allow-overlap": false,
              "text-ignore-placement": false
            },
            paint: {
              "text-color": "#ffffff",
              "text-halo-color": "#000000",
              "text-halo-width": 2
            },
            // minzoom: 14
          });
        } else {
          // Show existing layer
          mapRef.current.setLayoutProperty(priceLayerId, "visibility", "visible");
        }
      } else {
        // Hide prices when status or usage filters are active, or no price filter
        if (layerExists) {
          mapRef.current.setLayoutProperty(priceLayerId, "visibility", "none");
        }
      }
    } catch (error) {
      console.error("Error managing price display layer:", error);
    }
  };

  // Function to apply filters to map
  const applyFiltersToMap = (filters) => {
    if (!mapRef.current || !mapRef.current.isStyleLoaded()) {
      console.log('Map not ready yet, skipping filter application');
      return;
    }

    // Check if parcels layer exists
    try {
      if (!mapRef.current.getLayer("parcels-fill")) {
        console.log('Parcels layer not ready yet, skipping filter application');
        return;
      }
    } catch (error) {
      console.log('Error checking parcels layer, skipping filter application:', error);
      return;
    }

    console.log('Applying filters to map:', filters);
    let filterExpression = ["all"];

    // Check if different filter types are active
    const isPriceFilterActive = filters.priceRange &&
      (filters.priceRange.min > 0 || filters.priceRange.max < 1000000);
    const isStatusFilterActive = filters.salesStatus && filters.salesStatus.length > 0;
    const isUsageFilterActive = filters.services && filters.services.length > 0;

    // Manage price display layer based on active filters
    managePriceDisplayLayer(isPriceFilterActive, isStatusFilterActive, isUsageFilterActive);

    // If no filters are active, remove all filters
    const hasActiveFilters = isStatusFilterActive || isUsageFilterActive || isPriceFilterActive;

    if (!hasActiveFilters) {
      console.log('No active filters, removing all filters');
      // Remove all filters
      const layers = ["parcels-fill", "parcels-outline", "parcels-labels"];
      layers.forEach(layerId => {
        if (mapRef.current.getLayer(layerId)) {
          mapRef.current.setFilter(layerId, null);
        }
      });
      return;
    }

    // Sales status filter
    if (filters.salesStatus && filters.salesStatus.length > 0) {
      const statusFilter = ["in", ["get", "sales_status"], ["literal", filters.salesStatus]];
      filterExpression.push(statusFilter);
    }

    // Price range filter - simplified
    if (filters.priceRange && filters.priceRange.min !== undefined && filters.priceRange.max !== undefined) {
      const priceFilter = [
        "all",
        [">=", ["get", "price_with_tax"], filters.priceRange.min],
        ["<=", ["get", "price_with_tax"], filters.priceRange.max]
      ];
      filterExpression.push(priceFilter);
    }

    // Services filter - using regex match for comma-separated services
    if (filters.services && filters.services.length > 0) {
      const servicesFilters = filters.services.map(service => [
        "!=", ["index-of", service, ["to-string", ["get", "services_available"]]], -1
      ]);
      if (servicesFilters.length === 1) {
        filterExpression.push(servicesFilters[0]);
      } else if (servicesFilters.length > 1) {
        filterExpression.push(["any", ...servicesFilters]);
      }
    }

    // Apply filter to all parcel layers (including price layer)
    const layers = ["parcels-fill", "parcels-outline", "parcels-labels", "parcels-prices"];
    console.log('Filter expression:', filterExpression);

    layers.forEach(layerId => {
      if (mapRef.current.getLayer(layerId)) {
        if (filterExpression.length > 1) {
          console.log(`Applying filter to layer ${layerId}:`, filterExpression);
          mapRef.current.setFilter(layerId, filterExpression);
        } else {
          console.log(`Removing filter from layer ${layerId}`);
          mapRef.current.setFilter(layerId, null); // Remove filter if no conditions
        }
      }
    });
  };

  // Handle filters change
  const handleFiltersChange = (newFilters) => {
    console.log('Applying filters:', newFilters);
    setActiveFilters(newFilters);
    applyFiltersToMap(newFilters);
  };

  // Apply external filters when they change
  useEffect(() => {
    const applyFiltersWithDelay = () => {
      if (filters) {
        console.log('External filters changed:', filters);
        setActiveFilters(filters);
        applyFiltersToMap(filters);
      } else {
        // Build filters from individual props
        const combinedFilters = {
          salesStatus: statusFilters,
          priceRange: priceFilter,
          services: usageFilters
        };
        console.log('Combined filters from props:', combinedFilters);
        setActiveFilters(combinedFilters);
        applyFiltersToMap(combinedFilters);
      }
    };

    // Check if map is ready safely
    const isMapReady = () => {
      try {
        return mapRef.current &&
               mapRef.current.isStyleLoaded &&
               mapRef.current.isStyleLoaded() &&
               mapRef.current.getLayer("parcels-fill");
      } catch (error) {
        return false;
      }
    };

    // Apply filters immediately if map is ready, otherwise wait
    if (isMapReady()) {
      applyFiltersWithDelay();
    } else {
      // Wait for map to be ready
      const timer = setTimeout(() => {
        if (isMapReady()) {
          applyFiltersWithDelay();
        }
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [filters, statusFilters, priceFilter, usageFilters]);

  // Function to search for parcel by number
  const searchParcel = useCallback(async (parcelNumber) => {
    if (!mapRef.current) return;

    try {
      setIsLoading(true);
      setLoadingMessage(getTranslation("searchingParcel", language));

      // Use the search API to find parcels by number
      const response = await searchParcels(parcelNumber);
      const parcels = response.data.data;

      if (parcels.length > 0) {
        // Find exact match first, or use the first result
        const foundParcel = parcels.find(
          (parcel) => parcel.parcel_number.toString() === parcelNumber.toString()
        ) || parcels[0];

        // Check if the parcel is in the currently loaded map data
        const parcelFeature = mapRef.current.querySourceFeatures("parcels", {
          filter: ["==", "id", foundParcel.id],
        });

        if (parcelFeature.length > 0) {
          // Parcel is already loaded on the map
          const feature = parcelFeature[0];
          const bounds = new mapboxgl.LngLatBounds();
          feature.geometry.coordinates[0].forEach((coord) =>
            bounds.extend(coord)
          );

          mapRef.current.fitBounds(bounds, { padding: 50, maxZoom: 19 });

          // Select the parcel and show popup
          setSelectedParcelId(foundParcel.id);

          // Calculate center of parcel for popup
          const center = feature.geometry.coordinates[0].reduce(
            (acc, coord) => [acc[0] + coord[0], acc[1] + coord[1]],
            [0, 0]
          ).map(sum => sum / feature.geometry.coordinates[0].length);

          createParcelPopup(foundParcel, center);
        } else {
          // Parcel is not currently loaded - check if it's in a different project
          const parcelProjectId = foundParcel.block?.project_id;

          if (parcelProjectId && parcelProjectId !== selectedProject && onProjectChange) {
            // Switch to the correct project automatically
            setLoadingMessage(
              language === "ar"
                ? "تحميل المشروع الصحيح..."
                : "Loading correct project..."
            );

            // Change the project
            onProjectChange(parcelProjectId);

            // Wait a bit for the project to load, then try to find the parcel again
            setTimeout(async () => {
              try {
                // Try to find the parcel in the newly loaded data
                const parcelFeature = mapRef.current.querySourceFeatures("parcels", {
                  filter: ["==", "id", foundParcel.id],
                });

                if (parcelFeature.length > 0) {
                  // Parcel is now loaded on the map
                  const feature = parcelFeature[0];
                  const bounds = new mapboxgl.LngLatBounds();
                  feature.geometry.coordinates[0].forEach((coord) =>
                    bounds.extend(coord)
                  );

                  mapRef.current.fitBounds(bounds, { padding: 50, maxZoom: 19 });

                  // Select the parcel and show popup
                  setSelectedParcelId(foundParcel.id);

                  // Calculate center of parcel for popup
                  const center = feature.geometry.coordinates[0].reduce(
                    (acc, coord) => [acc[0] + coord[0], acc[1] + coord[1]],
                    [0, 0]
                  ).map(sum => sum / feature.geometry.coordinates[0].length);

                  createParcelPopup(foundParcel, center);
                } else {
                  // Fallback to coordinates if still not found
                  if (foundParcel.coordinates) {
                    const geometry = JSON.parse(foundParcel.coordinates);
                    const bounds = new mapboxgl.LngLatBounds();
                    geometry.coordinates[0].forEach((coord) => bounds.extend(coord));

                    mapRef.current.fitBounds(bounds, { padding: 50, maxZoom: 19 });

                    // Show parcel details with popup
                    setSelectedParcelId(foundParcel.id);

                    // Calculate center of parcel from coordinates
                    const center = geometry.coordinates[0].reduce(
                      (acc, coord) => [acc[0] + coord[0], acc[1] + coord[1]],
                      [0, 0]
                    ).map(sum => sum / geometry.coordinates[0].length);

                    createParcelPopup(foundParcel, center);
                  }
                }
              } catch (error) {
                console.error("Error finding parcel after project change:", error);
              }
            }, 3000); // Wait 3 seconds for project data to load

          } else {
            // Try to zoom using coordinates directly
            if (foundParcel.coordinates) {
              try {
                const geometry = JSON.parse(foundParcel.coordinates);
                const bounds = new mapboxgl.LngLatBounds();
                geometry.coordinates[0].forEach((coord) => bounds.extend(coord));

                mapRef.current.fitBounds(bounds, { padding: 50, maxZoom: 19 });

                // Show parcel details with popup
                setSelectedParcelId(foundParcel.id);

                // Calculate center of parcel from coordinates
                const center = geometry.coordinates[0].reduce(
                  (acc, coord) => [acc[0] + coord[0], acc[1] + coord[1]],
                  [0, 0]
                ).map(sum => sum / geometry.coordinates[0].length);

                createParcelPopup(foundParcel, center);

              } catch (coordError) {
                console.error("Error parsing parcel coordinates:", coordError);
                alert(
                  language === "ar"
                    ? `تم العثور على الوحدة ولكن لا يمكن عرضها على الخريطة`
                    : `Parcel found but cannot be displayed on map`
                );
              }
            } else {
              alert(
                language === "ar"
                  ? `تم العثور على الوحدة ولكن لا يمكن عرضها على الخريطة`
                  : `Parcel found but cannot be displayed on map`
              );
            }
          }
        }
      } else {
        // Show error message if parcel not found
        alert(
          language === "ar"
            ? `لم يتم العثور على وحدة برقم: ${parcelNumber}`
            : `Parcel not found with number: ${parcelNumber}`
        );
      }
    } catch (error) {
      console.error("Error searching for parcel:", error);
      alert(
        language === "ar"
          ? "حدث خطأ أثناء البحث عن الوحدة"
          : "Error occurred while searching for parcel"
      );
    } finally {
      setIsLoading(false);
    }
  }, [language, selectedProject, onProjectChange, projects]);

  // Helper function to create and show popup
  const createParcelPopup = (parcelData, coordinates) => {
    // Close existing popup if any
    if (popupRef.current) {
      popupRef.current.remove();
    }

    // Create popup content container
    const popupContainer = document.createElement('div');

    // Find project data
    const projectData = selectedProject ? projects.find(p => p.id === selectedProject) : projects[0];

    console.log("project ",projectData)
    console.log("projects ",projects)
    // Render React component into the container
    const root = ReactDOM.createRoot(popupContainer);
    root.render(
      React.createElement(ParcelPopupContent, {
        parcelData: parcelData,
        projectData: projectData,
        language: language,
        onVirtualTour: (parcelData) => {
          console.log("Virtual tour for parcel:", parcelData.parcel_number);
        },
        onShare: (parcelData) => {
          console.log("Share parcel:", parcelData.parcel_number);
        },
        onReserve: (parcelData) => {
          console.log("Reserve parcel:", parcelData.parcel_number);
        }
      })
    );

    // Create and show Mapbox popup
    const popup = new mapboxgl.Popup({
      closeButton: true,
      closeOnClick: false,
      maxWidth: '320px',
      className: 'parcel-popup'
    })
      .setLngLat(coordinates)
      .setDOMContent(popupContainer)
      .addTo(mapRef.current);

    // Store popup reference
    popupRef.current = popup;

    // Handle popup close
    popup.on('close', () => {
      setSelectedParcelId(null);
      setSelectedParcelData(null);
      setIsParcelPopoverVisible(false);
      popupRef.current = null;
    });

    // Update state
    setSelectedParcelData(parcelData);
    setIsParcelPopoverVisible(true);
  };

  // Pass search function to parent
  useEffect(() => {
    if (onParcelSearch && onParcelSearch.current !== undefined) {
      onParcelSearch.current = searchParcel;
    }
  }, [onParcelSearch, language, searchParcel]);

  // Function to create project marker with developer logo
  const createProjectMarker = (project, coordinates) => {
    if (!mapRef.current) return null;

    const markerContainer = document.createElement("div");
    markerContainer.style.display = "flex";
    markerContainer.style.flexDirection = "column";
    markerContainer.style.alignItems = "center";
    markerContainer.style.cursor = "pointer";

    // Create logo container
    const logoContainer = document.createElement("div");
    logoContainer.style.width = "60px";
    logoContainer.style.height = "60px";
    logoContainer.style.borderRadius = "50%";
    logoContainer.style.backgroundColor = "#ffffff";
    // Highlight selected project with different border color
    logoContainer.style.border = selectedProject === project.id ? "3px solid #ff6b35" : "3px solid #007cbf";
    logoContainer.style.boxShadow = selectedProject === project.id ? "0 6px 16px rgba(255, 107, 53, 0.4)" : "0 4px 8px rgba(0,0,0,0.3)";
    logoContainer.style.display = "flex";
    logoContainer.style.alignItems = "center";
    logoContainer.style.justifyContent = "center";
    logoContainer.style.overflow = "hidden";
    logoContainer.style.transition = "all 0.3s ease";

    if (project.developer_logo_url) {
      // If developer logo exists, use it
      const logoImg = document.createElement("img");
      logoImg.src = project.developer_logo_url;
      logoImg.style.width = "50px";
      logoImg.style.height = "50px";
      logoImg.style.borderRadius = "50%";
      logoImg.style.objectFit = "cover";
      logoImg.onerror = () => {
        // Fallback to default marker if image fails to load
        logoContainer.innerHTML = "";
        const fallbackMarker = document.createElement("div");
        fallbackMarker.style.width = "50px";
        fallbackMarker.style.height = "50px";
        fallbackMarker.style.borderRadius = "50%";
        fallbackMarker.style.backgroundColor = "#007cbf";
        logoContainer.appendChild(fallbackMarker);
      };
      logoContainer.appendChild(logoImg);
    } else {
      // Default marker if no logo
      const defaultMarker = document.createElement("div");
      defaultMarker.style.width = "50px";
      defaultMarker.style.height = "50px";
      defaultMarker.style.borderRadius = "50%";
      defaultMarker.style.backgroundColor = "#007cbf";
      logoContainer.appendChild(defaultMarker);
    }

    // Create project label
    const markerLabel = document.createElement("div");
    markerLabel.textContent = project.name || "Project";
    markerLabel.style.marginTop = "8px";
    markerLabel.style.fontSize = "12px";
    markerLabel.style.fontWeight = "bold";
    markerLabel.style.color = "#004080";
    markerLabel.style.backgroundColor = "rgba(255,255,255,0.95)";
    markerLabel.style.padding = "4px 8px";
    markerLabel.style.borderRadius = "6px";
    markerLabel.style.border = "1px solid #007cbf";
    markerLabel.style.whiteSpace = "nowrap";
    markerLabel.style.boxShadow = "0 2px 4px rgba(0,0,0,0.2)";

    markerContainer.appendChild(logoContainer);
    markerContainer.appendChild(markerLabel);

    // Create Mapbox marker
    const marker = new mapboxgl.Marker(markerContainer)
      .setLngLat(coordinates)
      .addTo(mapRef.current);

    // Add click event
    markerContainer.addEventListener("click", async () => {
      try {
        // Update selected project if different
        if (selectedProject !== project.id && onProjectChange) {
          onProjectChange(project.id);
        }

        // Use pre-loaded project data if statistics are available, otherwise fetch
        if (project.statistics && Object.keys(project.statistics).length > 0) {
          console.log("Using pre-loaded project data for fast display");
          // Show project details panel immediately with pre-loaded data
          setSelectedProjectData(project);
          setIsProjectPanelVisible(true);
        } else {
          console.log("Fetching detailed project data...");
          // Fetch detailed project data with statistics
          const response = await getProject(project.id);
          const projectData = response.data.data;

          // Show project details panel
          setSelectedProjectData(projectData);
          setIsProjectPanelVisible(true);
        }

        // Fly to project location
        mapRef.current.flyTo({ center: coordinates, zoom: 16 });
      } catch (error) {
        console.error("Error fetching project details:", error);
        // Fallback: use basic project data and fly to location
        setSelectedProjectData(project);
        setIsProjectPanelVisible(true);
        mapRef.current.flyTo({ center: coordinates, zoom: 16 });
      }
    });

    return marker;
  };

  // Function to update project markers - Show ALL projects or only shared project
  const updateProjectMarkers = (projectsData) => {
    // Clear existing markers
    projectMarkers.forEach((marker) => marker.remove());

    // Create markers for projects
    const newMarkers = [];

    // Filter projects based on shared view
    const projectsToShow = isSharedView && sharedProjectId
      ? projectsData.filter(project => project.id === sharedProjectId)
      : projectsData;

    console.log(`Showing ${projectsToShow.length} projects (isSharedView: ${isSharedView}, sharedProjectId: ${sharedProjectId})`);

    projectsToShow.forEach((project) => {
      // Skip projects without coordinates
      if (!project.latitude || !project.longitude) {
        console.warn(`Project ${project.name} has no coordinates, skipping marker`);
        return;
      }

      const coordinates = [
        parseFloat(project.longitude),
        parseFloat(project.latitude)
      ];

      const marker = createProjectMarker(project, coordinates);
      if (marker) {
        newMarkers.push(marker);

        // Add zoom-based visibility control
        const handleZoom = () => {
          const zoom = mapRef.current.getZoom();
          const markerElement = marker.getElement();

          // Show project markers at zoom levels below 12
          if (zoom < 12) {
            markerElement.style.display = "flex";
            // Highlight selected project marker
            if (selectedProject === project.id) {
              markerElement.style.transform = "scale(1.2)";
              markerElement.style.zIndex = "1000";
            } else {
              markerElement.style.transform = "scale(1)";
              markerElement.style.zIndex = "100";
            }
          } else {
            markerElement.style.display = "none";
          }
        };

        // Add zoom listener
        mapRef.current.on("zoom", handleZoom);

        // Store the cleanup function
        marker._zoomHandler = handleZoom;

        // Initial visibility check
        handleZoom();
      }
    });

    setProjectMarkers(newMarkers);
  };

  // Function to get icon path for landmark type
  const getLandmarkIcon = (type) => {
    const iconMap = {
      'mosque': APP_PATHS.MAP_ICONS.MOSQUE,
      'school': APP_PATHS.MAP_ICONS.SCHOOL,
      'hospital': APP_PATHS.MAP_ICONS.HOSPITAL,
      'shopping_center': APP_PATHS.MAP_ICONS.SHOPPING_CENTER,
      'park': APP_PATHS.MAP_ICONS.PARK,
      'gas_station': APP_PATHS.MAP_ICONS.GAS_STATION,
      'restaurant': APP_PATHS.MAP_ICONS.RESTAURANT,
      'bank': APP_PATHS.MAP_ICONS.BANK,
      'pharmacy': APP_PATHS.MAP_ICONS.PHARMACY,
      'other': APP_PATHS.MAP_ICONS.OTHER
    };
    return iconMap[type] || APP_PATHS.MAP_ICONS.DEFAULT;
  };

  // Function to get Arabic name for landmark type
  const getLandmarkTypeArabic = (type) => {
    const typeMap = {
      'mosque': 'مسجد',
      'school': 'مدرسة',
      'hospital': 'مستشفى',
      'shopping_center': 'مركز تسوق',
      'park': 'حديقة',
      'gas_station': 'محطة وقود',
      'restaurant': 'مطعم',
      'bank': 'بنك',
      'pharmacy': 'صيدلية',
      'other': 'أخرى'
    };
    return typeMap[type] || 'نقطة اهتمام';
  };

  const drowLandMark = async (projectId) => {
    console.log(`start land Mark ...`);

    // Clean up existing landmark markers
    if (mapRef.current._landmarkMarkers) {
      mapRef.current._landmarkMarkers.forEach((marker) => {
        marker.remove();
      });
      mapRef.current._landmarkMarkers = [];
    }

    // Clean up zoom handler
    if (mapRef.current._landmarkZoomHandler) {
      mapRef.current.off("zoom", mapRef.current._landmarkZoomHandler);
      mapRef.current._landmarkZoomHandler = null;
    }

    // Clean up landmark data
    if (mapRef.current._landmarkData) {
      mapRef.current._landmarkData.forEach(landmarkItem => {
        if (landmarkItem.marker) {
          landmarkItem.marker.remove();
        }
      });
      mapRef.current._landmarkData = [];
    }

    const response = await getProjectLandmarks(projectId);
    const layersData = response.data.data;
    console.log(`start :: `, layersData);

    // Store landmark markers for cleanup
    const landmarkMarkers = [];

    // Create landmark data structure for zoom-based management
    const landmarkData = layersData.map(landmark => ({
      ...landmark,
      marker: null,
      isVisible: false
    }));

    // Function to create marker element
    const createMarkerElement = (landmark) => {
      // Create a container for the marker
      const markerContainer = document.createElement('div');
      markerContainer.style.display = 'flex';
      markerContainer.style.flexDirection = 'column';
      markerContainer.style.alignItems = 'center';
      markerContainer.style.cursor = 'pointer';
      markerContainer.className = 'landmark-marker';

      // Create icon container
      const iconContainer = document.createElement('div');
      iconContainer.style.width = '44px';
      iconContainer.style.height = '44px';
      iconContainer.style.display = 'flex';
      iconContainer.style.alignItems = 'center';
      iconContainer.style.justifyContent = 'center';
      iconContainer.style.backgroundColor = 'rgba(255, 255, 255, 0.98)';
      iconContainer.style.borderRadius = '50%';
      iconContainer.style.border = '2px solid #1f2937';
      iconContainer.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.25)';
      iconContainer.style.padding = '8px';
      iconContainer.style.transition = 'all 0.2s ease';

      // Create icon image element
      const iconElement = document.createElement('img');
      iconElement.src = landmark.icon_path ?
        (landmark.icon_path.startsWith('http') ? landmark.icon_path : getStorageUrl(landmark.icon_path)) :
        getLandmarkIcon(landmark.type);
      iconElement.style.width = '28px';
      iconElement.style.height = '28px';
      iconElement.style.objectFit = 'contain';
      iconElement.style.filter = 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1))';
      iconElement.alt = landmark.name;

      // Add error handling for missing images
      iconElement.onerror = () => {
        // If the custom icon fails, try the default type icon
        const defaultIcon = getLandmarkIcon(landmark.type);
        if (iconElement.src !== defaultIcon) {
          iconElement.src = defaultIcon;
        } else {
          // If even the default icon fails, use a fallback
          iconElement.src = '/map_icons/Socity center icon.png';
        }
      };

      // Create label element
      const labelElement = document.createElement('div');
      labelElement.textContent = landmark.name;
      labelElement.style.marginTop = '4px';
      labelElement.style.fontSize = '11px';
      labelElement.style.fontWeight = 'bold';
      labelElement.style.color = '#004080';
      labelElement.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
      labelElement.style.padding = '2px 6px';
      labelElement.style.borderRadius = '4px';
      labelElement.style.border = '1px solid #007cbf';
      labelElement.style.whiteSpace = 'nowrap';
      labelElement.style.boxShadow = '0 1px 4px rgba(0, 0, 0, 0.2)';
      labelElement.style.maxWidth = '120px';
      labelElement.style.overflow = 'hidden';
      labelElement.style.textOverflow = 'ellipsis';

      // Add hover effects
      markerContainer.addEventListener('mouseenter', () => {
        iconContainer.style.transform = 'scale(1.1)';
        iconContainer.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.35)';
        labelElement.style.backgroundColor = 'rgba(255, 255, 255, 1)';
      });

      markerContainer.addEventListener('mouseleave', () => {
        iconContainer.style.transform = 'scale(1)';
        iconContainer.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.25)';
        labelElement.style.backgroundColor = 'rgba(255, 255, 255, 0.95)';
      });

      // Add elements to container
      iconContainer.appendChild(iconElement);
      markerContainer.appendChild(iconContainer);
      markerContainer.appendChild(labelElement);

      // Add click event
      markerContainer.addEventListener('click', () => {
        const message = `${landmark.name}\n${getLandmarkTypeArabic(landmark.type)}${landmark.description ? '\n' + landmark.description : ''}`;
        window.alert(message);
      });

      return markerContainer;
    };

    // Function to handle zoom changes for landmarks
    const handleLandmarkZoomChange = () => {
      const currentZoom = mapRef.current.getZoom();

      landmarkData.forEach(landmarkItem => {
        const minZoom = landmarkItem.min_zoom || 14;
        const maxZoom = landmarkItem.max_zoom || 22;
        const shouldBeVisible = currentZoom >= minZoom && currentZoom <= maxZoom;

        if (shouldBeVisible && !landmarkItem.isVisible) {
          // Create and add marker
          const markerElement = createMarkerElement(landmarkItem);
          const marker = new mapboxgl.Marker(markerElement)
            .setLngLat([landmarkItem.longitude, landmarkItem.latitude])
            .addTo(mapRef.current);

          landmarkItem.marker = marker;
          landmarkItem.isVisible = true;
          landmarkMarkers.push(marker);
        } else if (!shouldBeVisible && landmarkItem.isVisible) {
          // Remove marker
          if (landmarkItem.marker) {
            landmarkItem.marker.remove();
            const index = landmarkMarkers.indexOf(landmarkItem.marker);
            if (index > -1) {
              landmarkMarkers.splice(index, 1);
            }
            landmarkItem.marker = null;
            landmarkItem.isVisible = false;
          }
        }
      });
    };

    // Set initial state based on current zoom
    handleLandmarkZoomChange();

    // Add zoom listener
    mapRef.current.on('zoom', handleLandmarkZoomChange);

    // Store the zoom handler for cleanup
    mapRef.current._landmarkZoomHandler = handleLandmarkZoomChange;
    mapRef.current._landmarkData = landmarkData;

    // Store landmark markers for cleanup
    if (!mapRef.current._landmarkMarkers) {
      mapRef.current._landmarkMarkers = [];
    }
    mapRef.current._landmarkMarkers = landmarkMarkers;

    console.log(`Loaded ${layersData.length} landmarks for project ${projectId}`);
  };
  // Function to clear existing map layers
  const clearMapLayers = () => {
    if (!mapRef.current) return;

    // Remove existing map layer sources and layers
    mapLayers.forEach((layer) => {
      const layerId = `${layer.name}-layer`;
      const sourceId = layer.name;

      try {
        // Remove layer if it exists
        if (mapRef.current.getLayer(layerId)) {
          mapRef.current.removeLayer(layerId);
        }

        // For GeoJSON layers - remove all possible layer types
        const geojsonLayerIds = [
          `${layer.name}-fill`,
          `${layer.name}-outline`,
          `${layer.name}-line`,
          `${layer.name}-circle`,
          `${layer.name}-labels`
        ];

        geojsonLayerIds.forEach(id => {
          if (mapRef.current.getLayer(id)) {
            mapRef.current.removeLayer(id);
          }
        });

        // Remove source if it exists
        if (mapRef.current.getSource(sourceId)) {
          mapRef.current.removeSource(sourceId);
        }
      } catch (error) {
        console.warn(`Error removing layer ${layerId}:`, error);
      }
    });

    // Clear landmarks
    if (mapRef.current._landmarkMarkers) {
      mapRef.current._landmarkMarkers.forEach((marker) => {
        marker.remove();
      });
      mapRef.current._landmarkMarkers = [];
    }

    // Clear landmark data
    if (mapRef.current._landmarkData) {
      mapRef.current._landmarkData.forEach(landmarkItem => {
        if (landmarkItem.marker) {
          landmarkItem.marker.remove();
        }
      });
      mapRef.current._landmarkData = [];
    }

    console.log("Cleared existing map layers and landmarks");
  };

  // Function to load and add map layers
  const loadMapLayers = async (projectId) => {
    try {
      // Clear existing layers first
      clearMapLayers();

      setLoadingMessage(getTranslation("loadingMapLayers", language));
      const response = await getProjectMapLayers(projectId);

      const layersData = response.data.data;
      setMapLayers(layersData);

      if (mapRef.current && layersData.length > 0) {
        await addMapLayersToMap(layersData);
      }

      // Load landmarks for this project
      drowLandMark(projectId);

      console.log(
        `Loaded ${layersData.length} map layers for project ${projectId}`
      );

      // List all currently loaded map layers for debugging
      if (mapRef.current) {
        const mapLayers = mapRef.current.getStyle().layers;
        const rasterLayers = mapLayers.filter(
          (layer) => layer.type === "raster"
        );
        console.log(
          `Total raster layers on map: ${rasterLayers.length}`,
          rasterLayers.map((layer) => layer.id)
        );
      }
    } catch (error) {
      console.error("Error loading map layers:", error);
    }
  };

  // Function to add map layers to the map
  const addMapLayersToMap = async (layersData) => {
    if (!mapRef.current) {
      console.error("Error addMapLayersToMap empty map ");
      return;
    }

    // Remove existing map layers
    layersData.forEach((layer) => {
      // For image layers
      const layerId = `${layer.name}-layer`;
      if (mapRef.current.getLayer(layerId)) {
        mapRef.current.removeLayer(layerId);
      }

      // For GeoJSON layers - remove all possible layer types
      const geojsonLayerIds = [
        `${layer.name}-fill`,
        `${layer.name}-outline`,
        `${layer.name}-line`,
        `${layer.name}-circle`,
        `${layer.name}-labels`
      ];

      geojsonLayerIds.forEach(id => {
        if (mapRef.current.getLayer(id)) {
          mapRef.current.removeLayer(id);
        }
      });

      // Remove the source
      if (mapRef.current.getSource(layer.name)) {
        mapRef.current.removeSource(layer.name);
      }
    });

    // Add new map layers
    for (const layer of layersData) {
      if (layer.type === "image" && layer.is_active) {
        try {
          console.log(`Processing layer: ${layer.name}`, {
            coordinates: layer.coordinates,
            coordinatesType: typeof layer.coordinates,
          });

          let coordinates;
          if (typeof layer.coordinates === "string") {
            // Clean the string and parse it
            const cleanCoordinates = layer.coordinates.trim();
            coordinates = JSON.parse(cleanCoordinates);
          } else if (Array.isArray(layer.coordinates)) {
            coordinates = layer.coordinates;
          } else {
            console.error(
              `Invalid coordinates format for layer ${layer.name}:`,
              layer.coordinates
            );
            return;
          }

          // Validate coordinates format
          if (!Array.isArray(coordinates) || coordinates.length !== 4) {
            console.error(
              `Invalid coordinates array for layer ${layer.name}:`,
              coordinates
            );
            return;
          }

          // Extract filename from file_path
          const filename = layer.file_path.split("/").pop();

          mapRef.current.addSource(layer.name, {
            type: "image",
            url: getMapLayerFileUrl(filename),
            coordinates: coordinates,
          });

          mapRef.current.addLayer({
            id: `${layer.name}-layer`,
            type: "raster",
            source: layer.name,
            paint: {
              "raster-opacity": parseFloat(layer.opacity) || 1.0,
            },
            // minzoom: parseInt(layer.min_zoom) || 0,
            // maxzoom: parseInt(layer.max_zoom) || 22,
          });

          console.log(`Successfully added map layer: ${layer.name}`, {
            opacity: parseFloat(layer.opacity),
            minZoom: parseInt(layer.min_zoom),
            maxZoom: parseInt(layer.max_zoom),
            fileUrl: getMapLayerFileUrl(filename),
          });
        } catch (error) {
          console.error(`Error adding map layer ${layer.name}:`, error);
          console.error(`Layer data:`, layer);
        }
      }
      // GeoJSON layers
      else if (layer.type === "geojson" && layer.is_active) {
        try {
          console.log(`Processing GeoJSON layer: ${layer.name}`);

          // Extract filename from file_path
          const filename = layer.file_path.split("/").pop();
          const geojsonUrl = getMapLayerFileUrl(filename);

          // Fetch GeoJSON data
          const response = await fetch(geojsonUrl);
          if (!response.ok) {
            throw new Error(`Failed to fetch GeoJSON: ${response.statusText}`);
          }

          const geojsonData = await response.json();

          // Add GeoJSON source with data
          mapRef.current.addSource(layer.name, {
            type: "geojson",
            data: geojsonData,
          });

          // Determine layer type based on geometry or layer configuration
          let layerType = layer.layer_type;

          // Auto-detect layer type from GeoJSON geometry if not specified
          if (!layerType && geojsonData.features && geojsonData.features.length > 0) {
            const firstFeature = geojsonData.features[0];
            const geometryType = firstFeature.geometry.type;

            switch (geometryType) {
              case "Point":
              case "MultiPoint":
                layerType = "circle";
                break;
              case "LineString":
              case "MultiLineString":
                layerType = "line";
                break;
              case "Polygon":
              case "MultiPolygon":
                layerType = "fill";
                break;
              default:
                layerType = "circle"; // Default fallback
            }

            console.log(`Auto-detected layer type: ${layerType} for geometry: ${geometryType}`);
          } else {
            layerType = layerType || "fill"; // Default to fill if no detection possible
          }

          if (layerType === "fill") {
            // Add fill layer for polygons
            mapRef.current.addLayer({
              id: `${layer.name}-fill`,
              type: "fill",
              source: layer.name,
              paint: {
                "fill-color": layer.fill_color || "#0074D9",
                "fill-opacity": parseFloat(layer.opacity) || 0.6,
              },
              minzoom: parseInt(layer.min_zoom) || 0,
              maxzoom: parseInt(layer.max_zoom) || 22,
            });

            // Add outline layer
            mapRef.current.addLayer({
              id: `${layer.name}-outline`,
              type: "line",
              source: layer.name,
              paint: {
                "line-color": layer.stroke_color || "#0074D9",
                "line-width": parseInt(layer.stroke_width) || 2,
                "line-opacity": parseFloat(layer.opacity) || 1.0,
              },
              minzoom: parseInt(layer.min_zoom) || 0,
              maxzoom: parseInt(layer.max_zoom) || 22,
            });
          } else if (layerType === "line") {
            // Add line layer for linestrings
            mapRef.current.addLayer({
              id: `${layer.name}-line`,
              type: "line",
              source: layer.name,
              paint: {
                "line-color": layer.stroke_color || "#0074D9",
                "line-width": parseInt(layer.stroke_width) || 2,
                "line-opacity": parseFloat(layer.opacity) || 1.0,
              },
              minzoom: parseInt(layer.min_zoom) || 0,
              maxzoom: parseInt(layer.max_zoom) || 22,
            });
          } else if (layerType === "circle") {
            // Add circle layer for points
            mapRef.current.addLayer({
              id: `${layer.name}-circle`,
              type: "circle",
              source: layer.name,
              paint: {
                "circle-color": layer.fill_color || "#ff6b35",
                "circle-radius": parseInt(layer.circle_radius) || 3,
                "circle-opacity": parseFloat(layer.opacity) || 0.8,
                "circle-stroke-color": layer.stroke_color || "#ffffff",
                "circle-stroke-width": parseInt(layer.stroke_width) || 1,
              },
              minzoom: parseInt(layer.min_zoom) || 0,
              ...(layer.max_zoom && { maxzoom: parseInt(layer.max_zoom) }),
            });

            // Add labels for points if they have text properties
            if (geojsonData.features && geojsonData.features.length > 0) {
              const firstFeature = geojsonData.features[0];
              const hasTextProperty = firstFeature.properties &&
                (firstFeature.properties.length || firstFeature.properties.label || firstFeature.properties.name);

              if (hasTextProperty) {
                mapRef.current.addLayer({
                  id: `${layer.name}-labels`,
                  type: "symbol",
                  source: layer.name,
                  layout: {
                    "text-field": ["get", "length"] || ["get", "label"] || ["get", "name"] || "",
                    "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
                    "text-size": 8,
                    "text-offset": [0, 2],
                    "text-anchor": "top",
                  },
                  paint: {
                    "text-color": "#000000",
                    "text-halo-color": "#ffffff",
                    "text-halo-width": 1,
                  },
                  
                  minzoom: parseInt(layer.min_zoom) || 0,
                  ...(layer.max_zoom && { maxzoom: parseInt(layer.max_zoom) }),
                });
              }
            }
          }

          console.log(`Successfully added GeoJSON layer: ${layer.name}`, {
            type: layerType,
            opacity: parseFloat(layer.opacity),
            minZoom: parseInt(layer.min_zoom),
            maxZoom: parseInt(layer.max_zoom),
            geojsonUrl: geojsonUrl,
          });
        } catch (error) {
          console.error(`Error adding GeoJSON layer ${layer.name}:`, error);
          console.error(`Layer data:`, layer);
        }
      }
    }
  };

  // Function to update map styling based on selection
  const updateMapStyling = () => {
    if (!mapRef.current) return;

    // Update block styling
    if (mapRef.current.getLayer("blocks-fill")) {
      mapRef.current.setPaintProperty("blocks-fill", "fill-color", [
        "case",
        ["==", ["get", "id"], selectedBlockId || -1],
        "#ff6b35", // Orange for selected block
        "#e0e0e0", // Default gray
      ]);

      mapRef.current.setPaintProperty("blocks-outline", "line-color", [
        "case",
        ["==", ["get", "id"], selectedBlockId || -1],
        "#ff4500", // Dark orange for selected block outline
        "#0074D9", // Default blue
      ]);

      mapRef.current.setPaintProperty("blocks-outline", "line-width", [
        "case",
        ["==", ["get", "id"], selectedBlockId || -1],
        5, // Thicker line for selected block
        3, // Default thickness
      ]);
    }

    // Update parcel styling
    if (mapRef.current.getLayer("parcels-fill")) {
      // Keep original colors based on sales status - no background change for selection
      mapRef.current.setPaintProperty("parcels-fill", "fill-color", [
        "case",
        ["==", ["get", "sales_status"], "Available"],
        "#10b981", // Emerald green
        ["==", ["get", "sales_status"], "Reserved"],
        "#f59e0b", // Amber
        ["==", ["get", "sales_status"], "Sold"],
        "#ef4444", // Red
        "#6b7280", // Gray
      ]);

      // Only change the outline color and width for selected parcels
      mapRef.current.setPaintProperty("parcels-outline", "line-color", [
        "case",
        ["==", ["get", "id"], selectedParcelId || -1],
        "#ffffff", // White border for selected parcel
        "#2980b9", // Default blue
      ]);

      mapRef.current.setPaintProperty("parcels-outline", "line-width", [
        "case",
        ["==", ["get", "id"], selectedParcelId || -1],
        4, // Thicker white line for selected parcel
        1, // Default thickness
      ]);
    }
  };

  // Function to zoom to block
  const zoomToBlock = (blockId) => {
    console.log("zoomToBlock called with:", blockId);
    console.log("mapRef.current:", !!mapRef.current);
    console.log("dataLoaded:", dataLoaded);

    if (!mapRef.current) {
      console.log("Cannot zoom: map not ready");
      return;
    }

    const source = mapRef.current.getSource("blocks");
    console.log("blocks source:", source);

    if (
      source &&
      source._data &&
      source._data.features &&
      source._data.features.length > 0
    ) {
      console.log("blocks features:", source._data.features.length);
      const block = source._data.features.find(
        (f) => f.properties.id == blockId
      );
      console.log("Found block:", block);

      if (block) {
        // Select the block
        setSelectedBlockId(blockId);
        setSelectedParcelId(null); // Clear parcel selection

        const bounds = new mapboxgl.LngLatBounds();
        block.geometry.coordinates[0].forEach((coord) => bounds.extend(coord));
        console.log("Zooming to bounds:", bounds);
        mapRef.current.fitBounds(bounds, { padding: 50, maxZoom: 18 });

        // Update styling after a short delay to ensure the map has updated
        setTimeout(() => updateMapStyling(), 100);
      } else {
        console.log("Block not found with ID:", blockId);
        console.log(
          "Available block IDs:",
          source._data.features.map((f) => f.properties.id)
        );
      }
    } else {
      console.log("No blocks source or features available");
      if (source) {
        console.log("Source data:", source._data);
      }
    }
  };

  // Function to zoom to parcel
  const zoomToParcel = (parcelId) => {
    console.log("zoomToParcel called with:", parcelId);
    console.log("mapRef.current:", !!mapRef.current);
    console.log("dataLoaded:", dataLoaded);

    if (!mapRef.current) {
      console.log("Cannot zoom: map not ready");
      return;
    }

    const source = mapRef.current.getSource("parcels");
    console.log("parcels source:", source);

    if (
      source &&
      source._data &&
      source._data.features &&
      source._data.features.length > 0
    ) {
      console.log("parcels features:", source._data.features.length);
      const parcel = source._data.features.find(
        (f) => f.properties.id == parcelId
      );
      console.log("Found parcel:", parcel);

      if (parcel) {
        // Select the parcel
        setSelectedParcelId(parcelId);
        setSelectedBlockId(null); // Clear block selection

        const bounds = new mapboxgl.LngLatBounds();
        parcel.geometry.coordinates[0].forEach((coord) => bounds.extend(coord));
        console.log("Zooming to parcel bounds:", bounds);
        mapRef.current.fitBounds(bounds, { padding: 20, maxZoom: 19 });

        // Update styling after a short delay to ensure the map has updated
        setTimeout(() => updateMapStyling(), 100);
      } else {
        console.log("Parcel not found with ID:", parcelId);
        console.log(
          "Available parcel IDs:",
          source._data.features.map((f) => f.properties.id)
        );
      }
    } else {
      console.log("No parcels source or features available");
      if (source) {
        console.log("Source data:", source._data);
      }
    }
  };

  // Function to recreate base layers after style change
  const recreateBaseLayers = () => {
    if (!mapRef.current) return;

    console.log("Recreating base layers...");

    try {
      // Add projects source for future use
      if (!mapRef.current.getSource("projects")) {
        mapRef.current.addSource("projects", {
          type: "geojson",
          data: { type: "FeatureCollection", features: [] },
        });
      }

      // Add blocks source and layers
      if (!mapRef.current.getSource("blocks")) {
        mapRef.current.addSource("blocks", {
          type: "geojson",
          data: { type: "FeatureCollection", features: [] },
        });
      }

      if (!mapRef.current.getLayer("blocks-fill")) {
        mapRef.current.addLayer({
          id: "blocks-fill",
          type: "fill",
          source: "blocks",
          paint: {
            "fill-color": "#e0e0e0",
            "fill-opacity": 0.5,
          },
          minzoom: 11,
          maxzoom: 14,
        });
      }

      if (!mapRef.current.getLayer("blocks-outline")) {
        mapRef.current.addLayer({
          id: "blocks-outline",
          type: "line",
          source: "blocks",
          paint: {
            "line-color": "#0074D9",
            "line-width": 3,
          },
          minzoom: 11,
          maxzoom: 14,
        });
      }

      if (!mapRef.current.getLayer("blocks-labels")) {
        mapRef.current.addLayer({
          id: "blocks-labels",
          type: "symbol",
          source: "blocks",
          layout: {
            "text-field": ["get", "block_number"],
            "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
            "text-size": 14,
          },
          paint: {
            "text-color": "#004080",
            "text-halo-color": "#ffffff",
            "text-halo-width": 2,
          },
          minzoom: 11,
          maxzoom: 14,
        });
      }

      // Add parcels source and layers
      if (!mapRef.current.getSource("parcels")) {
        mapRef.current.addSource("parcels", {
          type: "geojson",
          data: { type: "FeatureCollection", features: [] },
        });
      }

      if (!mapRef.current.getLayer("parcels-fill")) {
        mapRef.current.addLayer({
          id: "parcels-fill",
          type: "fill",
          source: "parcels",
          paint: {
            "fill-color": [
              "case",
              ["==", ["get", "sales_status"], "Available"],
              "#10b981", // Emerald green
              ["==", ["get", "sales_status"], "Reserved"],
              "#f59e0b", // Amber
              ["==", ["get", "sales_status"], "Sold"],
              "#ef4444", // Red
              "#6b7280", // Gray
            ],
            "fill-opacity": 0.8,
          },
          minzoom: 14,
        });
      }

      if (!mapRef.current.getLayer("parcels-outline")) {
        mapRef.current.addLayer({
          id: "parcels-outline",
          type: "line",
          source: "parcels",
          paint: {
            "line-color": "#2980b9",
            "line-width": 1,
          },
          minzoom: 14,
        });
      }

      if (!mapRef.current.getLayer("parcels-labels")) {
        mapRef.current.addLayer({
          id: "parcels-labels",
          type: "symbol",
          source: "parcels",
          layout: {
            "text-field": ["get", "parcel_number"],
            "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
            "text-size": 10,
          },
          paint: {
            "text-color": "#000000",
            "text-halo-color": "#ffffff",
            "text-halo-width": 1,
          },
          minzoom: 14,
        });
      }

      // Add price display layer if it doesn't exist (initially hidden)
      if (!mapRef.current.getLayer("parcels-prices")) {
        mapRef.current.addLayer({
          id: "parcels-prices",
          type: "symbol",
          source: "parcels",
          layout: {
            "text-field": [
              "format",
              ["to-string", ["get", "price_with_tax"]],
              {},
              " ر.س",
              {}
            ],
            "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
            "text-size": 10,
            "text-offset": [0, -1.5],
            "text-anchor": "center",
            "text-allow-overlap": false,
            "text-ignore-placement": false,
            "visibility": "none" // Initially hidden
          },
          paint: {
            "text-color": "#ffffff",
            "text-halo-color": "#000000",
            "text-halo-width": 2
          },
          minzoom: 14,
        });
      }

      // Re-add click events
      setupMapEvents();

      console.log("Base layers recreated successfully");
    } catch (error) {
      console.error("Error recreating base layers:", error);
    }
  };



  // Function to setup map events
  const setupMapEvents = () => {
    if (!mapRef.current) return;

    // Remove existing listeners to avoid duplicates
    mapRef.current.off("click", "blocks-fill");
    mapRef.current.off("click", "parcels-fill");
    mapRef.current.off("click");

    // Click events for blocks
    mapRef.current.on("click", "blocks-fill", (e) => {
      const feature = e.features[0];
      const blockId = feature.properties.id;

      // Select the block
      setSelectedBlockId(blockId);
      setSelectedParcelId(null);

      const bounds = new mapboxgl.LngLatBounds();
      feature.geometry.coordinates[0].forEach((coord) =>
        bounds.extend(coord)
      );
      mapRef.current.fitBounds(bounds, { padding: 50, maxZoom: 18 });
    });

    // Click events for parcels
    mapRef.current.on("click", "parcels-fill", (e) => {
      const feature = e.features[0];
      const properties = feature.properties;
      const parcelId = feature.properties.id;

      // Select the parcel
      setSelectedParcelId(parcelId);
      setSelectedBlockId(null);

      // Get the center of the parcel for popup positioning
      const coordinates = e.lngLat;

      // Create popup using helper function
      createParcelPopup(properties, coordinates);
    });

    // Clear selection when clicking on empty area
    mapRef.current.on("click", (e) => {
      // Check if click was on a feature
      const features = mapRef.current.queryRenderedFeatures(e.point, {
        layers: ["blocks-fill", "parcels-fill"],
      });

      if (features.length === 0) {
        // Clicked on empty area - clear selections and hide popover
        setSelectedParcelId(null);
        setSelectedBlockId(null);
        setSelectedParcelData(null);
        setIsParcelPopoverVisible(false);

        // Close popup if exists
        if (popupRef.current) {
          popupRef.current.remove();
          popupRef.current = null;
        }
      }
    });
  };

  // Load all data once
  const loadAllData = async () => {
    if (dataLoaded) return;

    try {
      // Start loading
      setIsLoading(true);
      setLoadingProgress(0);
      setLoadingMessage(getTranslation("loadingProjects", language));

      // Wait for map to be ready
      if (!mapRef.current || !mapRef.current.isStyleLoaded()) {
        console.log("Map not ready, waiting...");
        setTimeout(() => loadAllData(), 500);
        return;
      }

      // Load projects
      const projectsResponse = await getProjects();
      const projectsData = projectsResponse.data.data;
      setProjects(projectsData);
      setLoadingProgress(20);

      // Update project markers with developer logos
      updateProjectMarkers(projectsData);

      let allBlocks = [];
      let allParcels = [];

      // Load blocks and parcels for selected project or first available project
      const project = selectedProject
        ? projectsData.find((p) => p.id === selectedProject)
        : projectsData[0]; // Use first project if none selected

      // Load map layers for the selected project
      if (project) {
        await loadMapLayers(project.id);
        setLoadingProgress(30);
      }

      if (project) {
        setLoadingMessage(getTranslation("loadingBlocks", language));
        const blocksResponse = await getBlocks(project.id);
        const blocks = blocksResponse.data.data;
        setLoadingProgress(50);

        // Add all blocks first
        for (const block of blocks) {
          allBlocks.push({
            type: "Feature",
            geometry: JSON.parse(block.coordinates),
            properties: {
              id: block.id,
              block_number: block.block_number,
              project_id: block.project_id,
            },
          });
        }

        setLoadingMessage(getTranslation("loadingParcels", language));
        setLoadingProgress(70);

        // Load parcels for all blocks asynchronously
        const parcelPromises = blocks.map(async (block) => {
          try {
            const parcelsResponse = await getParcels(block.id);
            const parcels = parcelsResponse.data.data;

            // Update progress as we load each block's parcels
            const progressIncrement = 30 / blocks.length; // 30% for parcels loading
            setLoadingProgress((prev) =>
              Math.min(90, prev + progressIncrement)
            );

            return parcels.map((parcel) => ({
              type: "Feature",
              geometry: JSON.parse(parcel.coordinates),
              properties: {
                id: parcel.id,
                parcel_number: parcel.parcel_number,
                block_id: parcel.block_id,
                sales_status: parcel.sales_status,
                area: parseFloat(parcel.area) || 0,
                price_with_tax: parseFloat(parcel.price_with_tax) || 0,
                usage_type: parcel.usage_type,
                view: parcel.view || "",
                property_type: parcel.property_type || "",
                plot_condition: parcel.plot_condition || "",
                services_available: parcel.services_available || "",
              },
            }));
          } catch (error) {
            console.error(
              `Error loading parcels for block ${block.id}:`,
              error
            );
            return [];
          }
        });

        // Wait for all parcel requests to complete
        const parcelResults = await Promise.all(parcelPromises);
        allParcels = parcelResults.flat();

        // Store all parcels data for filtering
        setAllParcelsData(allParcels);

        setLoadingProgress(90);
      }

      setLoadingMessage(getTranslation("updatingMap", language));

      // Update map with all data
      if (mapRef.current && mapRef.current.isStyleLoaded()) {
        // Update projects - show ALL projects as features
        const projectFeatures = projectsData
          .filter(project => project.latitude && project.longitude) // Only projects with coordinates
          .map(project => ({
            type: "Feature",
            geometry: {
              type: "Point",
              coordinates: [
                parseFloat(project.longitude),
                parseFloat(project.latitude)
              ],
            },
            properties: {
              id: project.id,
              name: project.name,
              description: project.description,
              location: project.location,
              isSelected: selectedProject === project.id,
            },
          }));

        // Check if sources exist before updating
        const projectsSource = mapRef.current.getSource("projects");
        if (projectsSource) {
          projectsSource.setData({
            type: "FeatureCollection",
            features: projectFeatures,
          });
        }

        // Update blocks
        const blocksSource = mapRef.current.getSource("blocks");
        if (blocksSource) {
          blocksSource.setData({
            type: "FeatureCollection",
            features: allBlocks,
          });
        }

        // Update parcels
        const parcelsSource = mapRef.current.getSource("parcels");
        if (parcelsSource) {
          parcelsSource.setData({
            type: "FeatureCollection",
            features: allParcels,
          });
        }
      }

      setLoadingProgress(100);
      setLoadingMessage(getTranslation("allDataLoaded", language));

      // Small delay to show completion message
      setTimeout(() => {
        setIsLoading(false);
        setLoadingProgress(0);
        setLoadingMessage("");
      }, 500);

      setDataLoaded(true);

      // Apply any existing filters after data is loaded
      setTimeout(() => {
        const currentFilters = {
          salesStatus: statusFilters,
          priceRange: priceFilter,
          services: usageFilters
        };
        console.log('Applying filters after data load:', currentFilters);
        applyFiltersToMap(currentFilters);
      }, 500);

      // Notify parent component that map is ready
      if (onMapReady) {
        console.log("Sending map functions to parent:", {
          zoomToBlock,
          zoomToParcel,
        });
        onMapReady({ zoomToBlock, zoomToParcel });
      }

      console.log("All data loaded:", {
        projects: projectsData.length,
        blocks: allBlocks.length,
        parcels: allParcels.length,
      });
    } catch (error) {
      console.error("Error loading data:", error);
      setLoadingMessage(getTranslation("loadingError", language));
      setLoadingProgress(0);
      setTimeout(() => {
        setIsLoading(false);
        setLoadingProgress(0);
        setLoadingMessage("");
      }, 2000);
    }
  };

  useEffect(() => {
    const map = new mapboxgl.Map({
      container: mapContainerRef.current,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      center: MAPBOX_CONFIG.DEFAULT_CENTER, // مركز السعودية
      zoom: MAPBOX_CONFIG.DEFAULT_ZOOM,
      // Mobile optimizations
      touchZoomRotate: true,
      touchPitch: false,
      dragRotate: false,
      pitchWithRotate: false,
      dragPan: true,
      keyboard: true,
      doubleClickZoom: true,
      scrollZoom: true,
    });

    mapRef.current = map;

    // Make map instance globally available for controls
    window.mapInstance = map;

    map.on("load", () => {
      console.log("Map loaded successfully!");

      // Project markers will be created dynamically when data loads

      // Add projects source for future use
      map.addSource("projects", {
        type: "geojson",
        data: { type: "FeatureCollection", features: [] },
      });

      // Add blocks source
      map.addSource("blocks", {
        type: "geojson",
        data: { type: "FeatureCollection", features: [] },
      });

      map.addLayer({
        id: "blocks-fill",
        type: "fill",
        source: "blocks",
        paint: {
          "fill-color": "#e0e0e0",
          "fill-opacity": 0.5,
        },
        minzoom: 11,
        maxzoom: 14,
      });

      map.addLayer({
        id: "blocks-outline",
        type: "line",
        source: "blocks",
        paint: {
          "line-color": "#0074D9",
          "line-width": 3,
        },
        minzoom: 11,
        maxzoom: 14,
      });

      map.addLayer({
        id: "blocks-labels",
        type: "symbol",
        source: "blocks",
        layout: {
          "text-field": ["get", "block_number"],
          "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
          "text-size": 14,
        },
        paint: {
          "text-color": "#004080",
          "text-halo-color": "#ffffff",
          "text-halo-width": 2,
        },
        minzoom: 11,
        maxzoom: 14,
      });

      // Add parcels source
      map.addSource("parcels", {
        type: "geojson",
        data: { type: "FeatureCollection", features: [] },
      });

      map.addLayer({
        id: "parcels-fill",
        type: "fill",
        source: "parcels",
        paint: {
          "fill-color": [
            "case",
            ["==", ["get", "sales_status"], "Available"],
            "#10b981", // Emerald green
            ["==", ["get", "sales_status"], "Reserved"],
            "#f59e0b", // Amber
            ["==", ["get", "sales_status"], "Sold"],
            "#ef4444", // Red
            "#6b7280", // Gray
          ],
          "fill-opacity": 0.8,
        },
        minzoom: 14,
      });

      map.addLayer({
        id: "parcels-outline",
        type: "line",
        source: "parcels",
        paint: {
          "line-color": "#2980b9",
          "line-width": 1,
        },
        minzoom: 14,
      });

      map.addLayer({
        id: "parcels-labels",
        type: "symbol",
        source: "parcels",
        layout: {
          "text-field": ["get", "parcel_number"],
          "text-font": ["Open Sans Semibold", "Arial Unicode MS Bold"],
          "text-size": 10,
        },
        paint: {
          "text-color": "#000000",
          "text-halo-color": "#ffffff",
          "text-halo-width": 1,
        },
        minzoom: 14,
      });

      // Add price display layer (initially hidden)
      map.addLayer({
        id: "parcels-prices",
        type: "symbol",
        source: "parcels",
        layout: {
          "text-field": [
            "format",
            ["to-string", ["get", "price_with_tax"]],
            {},
            " ر.س",
            {}
          ],
          "text-font": ["Open Sans Bold", "Arial Unicode MS Bold"],
          "text-size": 10,
          "text-offset": [0, -1.5],
          "text-anchor": "center",
          "text-allow-overlap": false,
          "text-ignore-placement": false,
          "visibility": "none" // Initially hidden
        },
        paint: {
          "text-color": "#ffffff",
          "text-halo-color": "#000000",
          "text-halo-width": 2
        },
        minzoom: 14,
      });

      // Setup map events
      setupMapEvents();

      // Load data immediately when map loads with a small delay
      setTimeout(() => {
        loadAllData();
      }, 100);
    });

    return () => {
      // Clean up project markers
      projectMarkers.forEach((marker) => {
        if (marker._zoomHandler) {
          map.off("zoom", marker._zoomHandler);
        }
        marker.remove();
      });

      // Clean up landmark markers
      if (map._landmarkMarkers) {
        map._landmarkMarkers.forEach((marker) => {
          marker.remove();
        });
      }

      // Clean up landmark zoom handler
      if (map._landmarkZoomHandler) {
        map.off("zoom", map._landmarkZoomHandler);
      }

      // Clean up landmark data
      if (map._landmarkData) {
        map._landmarkData.forEach(landmarkItem => {
          if (landmarkItem.marker) {
            landmarkItem.marker.remove();
          }
        });
      }

      if (map) map.remove();
    };
  }, []);

  // Load data when selectedProject changes (but not on initial load)
  useEffect(() => {
    if (selectedProject && mapRef.current) {
      // Only reload if we have a different project
      setDataLoaded(false);
      setIsLoading(false); // Reset loading state
      setLoadingProgress(0);
      setLoadingMessage("");
      loadAllData();
    }
  }, [selectedProject]);

  // Update markers when projects change
  useEffect(() => {
    if (projects.length > 0 && mapRef.current) {
      updateProjectMarkers(projects);
    }
  }, [projects, selectedProject]);

  // Update map styling when selection changes
  useEffect(() => {
    updateMapStyling();
  }, [selectedBlockId, selectedParcelId]);

  // Update map style when mapStyle prop changes
  useEffect(() => {
    if (mapRef.current && mapRef.current.isStyleLoaded()) {
      console.log("Changing map style to:", mapStyle);

      // Store current data before style change
      const currentProjects = [...projects];
      const currentMapLayers = [...mapLayers];
      const currentSelectedBlockId = selectedBlockId;
      const currentSelectedParcelId = selectedParcelId;

      // Store current data from sources
      let currentBlocksData = null;
      let currentParcelsData = null;

      const blocksSource = mapRef.current.getSource("blocks");
      const parcelsSource = mapRef.current.getSource("parcels");

      if (blocksSource && blocksSource._data) {
        currentBlocksData = JSON.parse(JSON.stringify(blocksSource._data));
      }

      if (parcelsSource && parcelsSource._data) {
        currentParcelsData = JSON.parse(JSON.stringify(parcelsSource._data));
      }

      console.log("Stored data:", {
        projects: currentProjects.length,
        mapLayers: currentMapLayers.length,
        blocks: currentBlocksData?.features?.length || 0,
        parcels: currentParcelsData?.features?.length || 0
      });

      mapRef.current.setStyle(`mapbox://styles/mapbox/${mapStyle}`);

      // Re-add sources and layers after style change
      mapRef.current.once("styledata", () => {
        console.log("Style loaded, restoring layers...");

        // Wait for style to be fully loaded
        const restoreEverything = () => {
          if (mapRef.current && mapRef.current.isStyleLoaded()) {
            console.log("Recreating base layers...");
            // Re-create all base sources and layers first
            recreateBaseLayers();

            // Restore data immediately
            setTimeout(async () => {
              console.log("Restoring all data...");

              // Restore project markers
              if (currentProjects.length > 0) {
                updateProjectMarkers(currentProjects);
              }

              // Restore map layers (images)
              if (currentMapLayers.length > 0) {
                await addMapLayersToMap(currentMapLayers);
              }

              // Restore blocks and parcels data
              if (currentBlocksData && currentBlocksData.features) {
                const blocksSource = mapRef.current.getSource("blocks");
                if (blocksSource) {
                  blocksSource.setData(currentBlocksData);
                  console.log("Restored blocks:", currentBlocksData.features.length);
                }
              }

              if (currentParcelsData && currentParcelsData.features) {
                const parcelsSource = mapRef.current.getSource("parcels");
                if (parcelsSource) {
                  parcelsSource.setData(currentParcelsData);
                  console.log("Restored parcels:", currentParcelsData.features.length);
                }
              }

              // Restore selections
              if (currentSelectedBlockId) {
                setSelectedBlockId(currentSelectedBlockId);
              }
              if (currentSelectedParcelId) {
                setSelectedParcelId(currentSelectedParcelId);
              }

              // Update styling
              setTimeout(() => {
                updateMapStyling();
                console.log("Map restoration complete!");
              }, 100);

            }, 200);
          } else {
            console.log("Style not ready, retrying...");
            setTimeout(restoreEverything, 100);
          }
        };

        restoreEverything();
      });
    }
  }, [mapStyle]);

  return (
    <div style={{ position: "relative", height: "100%", width: "100%" }}>
      <div ref={mapContainerRef} style={{ height: "100%", width: "100%" }} />

      <MapControls
        mapStyle={mapStyle}
        onMapStyleChange={onMapStyleChange}
        onLanguageToggle={onLanguageToggle}
        language={language}
        mapRef={mapRef}
      />

      <LoadingOverlay
        isVisible={isLoading}
        progress={loadingProgress}
        message={loadingMessage}
      />



      <MapFilters
        isVisible={filtersVisible}
        onClose={onToggleFilters}
        onFiltersChange={handleFiltersChange}
        language={language}
        parcelsData={allParcelsData}
      />

      <RealMapLogo />

      <RightControls mapRef={mapRef} language={language} />

      <TermsNotification />

      <ProjectDetailsPanel
        projectData={selectedProjectData}
        language={language}
        isVisible={isProjectPanelVisible}
        onClose={handleCloseProjectPanel}
      />
    </div>
  );
};

export default OptimizedMap;
