import React, { useEffect, useState } from "react";
import "./NorthArrow.css";

const NorthArrow = ({ mapRef }) => {
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    if (!mapRef?.current) return;

    const map = mapRef.current;

    // Function to update arrow rotation based on map bearing
    const updateRotation = () => {
      const bearing = map.getBearing();
      setRotation(-bearing); // Negative to counter-rotate the arrow
    };

    // Initial rotation
    updateRotation();

    // Listen for map rotation changes
    map.on('rotate', updateRotation);

    // Cleanup
    return () => {
      if (map) {
        map.off('rotate', updateRotation);
      }
    };
  }, [mapRef]);

  return (
    <div
      className="north-arrow-container"
      onClick={() => {
        // Reset map bearing to north when clicked
        if (mapRef?.current) {
          mapRef.current.easeTo({ bearing: 0, duration: 500 });
        }
      }}
      title="اضغط لإعادة توجيه الخريطة للشمال / Click to reset map to north"
    >
      {/* North Arrow Icon */}
      <div
        className="north-arrow-icon"
        style={{
          transform: `rotate(${rotation}deg)`,
        }}
      >
        <img src="/map_controls/RealMap North v3.png" alt="North Arrow" />
      </div>
    </div>
  );
};

export default NorthArrow;
