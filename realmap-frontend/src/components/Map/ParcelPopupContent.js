import React from "react";
import { getTranslation } from "../../utils/translations";
import { APP_PATHS } from "../../config/constants";
import "./ParcelPopupContent.css";

const ParcelPopupContent = ({ parcelData, projectData, language, onVirtualTour, onShare, onReserve }) => {
  const formatPrice = (price) => {
    if (!price) return "N/A";
    return new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(price);
  };

  const formatArea = (area) => {
    if (!area) return "N/A";
    return new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(area);
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return "#22c55e";
      case "reserved":
        return "#f59e0b";
      case "sold":
        return "#ef4444";
      default:
        return "#6b7280";
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return getTranslation("available", language);
      case "reserved":
        return getTranslation("reserved", language);
      case "sold":
        return getTranslation("sold", language);
      default:
        return status || "N/A";
    }
  };

  const handleVirtualTourClick = () => {
    console.log("Virtual tour clicked for parcel:", parcelData.parcel_number);
    if (onVirtualTour) onVirtualTour(parcelData);
  };

  const handleShareClick = () => {
    console.log("Share clicked for parcel:", parcelData.parcel_number);
    if (onShare) onShare(parcelData);
  };

  const handleReserveClick = () => {
    console.log("Reserve unit clicked for parcel:", parcelData.parcel_number);
    if (onReserve) onReserve(parcelData);
  };

  // Get the image source with fallback logic
  const getImageSource = () => {
    // Check if parcel has images
    if (parcelData.images && parcelData.images.length > 0) {
      const firstImage = parcelData.images[0];
      return typeof firstImage === 'string' ? firstImage : firstImage?.url;
    }

    console.log("Parcel has no images, falling back to project default", );

    // Fallback to project default image
    if (projectData?.default_unit_image_url) {
      return projectData.default_unit_image_url;
    }

    // Final fallback to logo
    return APP_PATHS.PUBLIC_ASSETS.LOGO_ONLY;
  };

  return (
    <div className={`parcel-popup-content ${language === "ar" ? "rtl" : "ltr"}`} dir={language === "ar" ? "rtl" : "ltr"}>
      {/* Header */}
      <div className="popup-header">
        <div className="popup-title">
          <h3>
            {getTranslation("parcel", language)} {parcelData.parcel_number}
          </h3>
          <div
            className="status-badge"
            style={{ backgroundColor: getStatusColor(parcelData.sales_status) }}
          >
            {getStatusText(parcelData.sales_status)}
          </div>
        </div>
        
        {/* Action Buttons */}
        <div className="action-buttons">
          <button className="virtual-tour-btn" onClick={handleVirtualTourClick} title={getTranslation("virtualTour", language)}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
              <path d="M12 2a10 10 0 0 1 0 20" stroke="currentColor" strokeWidth="1"/>
              <path d="M2 12h20" stroke="currentColor" strokeWidth="1"/>
              <path d="M12 2c-2.5 3-2.5 7 0 10s2.5 7 0 10" stroke="currentColor" strokeWidth="1"/>
              <path d="M12 2c2.5 3 2.5 7 0 10s-2.5 7 0 10" stroke="currentColor" strokeWidth="1"/>
              <text x="12" y="16" textAnchor="middle" fontSize="6" fill="currentColor">360°</text>
            </svg>
          </button>

          <button className="share-btn" onClick={handleShareClick} title={getTranslation("share", language) || "Share"}>
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z" fill="currentColor"/>
            </svg>
          </button>
        </div>
      </div>

      {/* Unit Image */}
      <div className="popup-image">
        <img
          src={getImageSource()}
          alt={`${getTranslation("parcel", language)} ${parcelData.parcel_number}`}
          onError={(e) => {
            // Fallback hierarchy: parcel image -> project default -> logo
            const currentSrc = e.target.src;

            if (parcelData.images && parcelData.images.length > 0 &&
                currentSrc.includes(typeof parcelData.images[0] === 'string' ? parcelData.images[0] : parcelData.images[0]?.url)) {
              // Failed to load parcel image, try project default
              if (projectData?.default_unit_image_url) {
                e.target.src = projectData.default_unit_image_url;
                return;
              }
            }

            if (projectData?.default_unit_image_url && currentSrc.includes(projectData.default_unit_image_url)) {
              // Failed to load project default, use logo
              e.target.src = APP_PATHS.PUBLIC_ASSETS.LOGO_ONLY;
              return;
            }

            // Final fallback
            if (!currentSrc.includes(APP_PATHS.PUBLIC_ASSETS.LOGO_ONLY)) {
              e.target.src = APP_PATHS.PUBLIC_ASSETS.LOGO_ONLY;
            }
          }}
        />
      </div>

      {/* Details */}
      <div className="popup-details">
        <div className="detail-row">
          <span className="detail-label">{getTranslation("unitNumber", language)}:</span>
          <span className="detail-value" dir="ltr">{parcelData.parcel_number}</span>
        </div>

        <div className="detail-row">
          <span className="detail-label">{getTranslation("areaLabel", language)}:</span>
          <span className="detail-value" dir="ltr">
            {formatArea(parcelData.area)} {getTranslation("sqm", language)}
          </span>
        </div>

        <div className="detail-row">
          <span className="detail-label">{getTranslation("priceLabel", language)}:</span>
          <span className="detail-value price-value" dir="ltr">
            {formatPrice(parcelData.total_price || 200000)} {getTranslation("sar", language)}
          </span>
        </div>
      </div>

      {/* Reserve Button */}
      {parcelData.sales_status?.toLowerCase() === "available" && (
        <button className="reserve-btn" onClick={handleReserveClick}>
          {getTranslation("reserveUnit", language)}
        </button>
      )}
    </div>
  );
};

export default ParcelPopupContent;
