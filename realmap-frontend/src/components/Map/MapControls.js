import React from "react";
import { FaSatellite, FaMap, FaPlus, FaMinus, FaHome, FaGlobe } from "react-icons/fa";
import "./MapControls.css";
import { getTranslation } from "../../utils/translations";

const MapControls = ({
  mapStyle,
  onMapStyleChange,
  onLanguageToggle,
  language,
  mapRef,
}) => {
  const mapStyles = [
    {
      id: "satellite-v9",
      name: getTranslation("satellite", language),
      icon: <FaSatellite />,
    },
    {
      id: "streets-v11",
      name: getTranslation("streets", language),
      icon: <FaMap />,
    },
  ];

  const currentStyleIndex = mapStyles.findIndex(
    (style) => style.id === mapStyle
  );

  const handleMapStyleToggle = () => {
    const nextIndex = (currentStyleIndex + 1) % mapStyles.length;
    onMapStyleChange(mapStyles[nextIndex].id);
  };

  return (
    <div className="map-controls">
      {/* Map Style Toggle Button */}
     

      {/* Language Toggle Button */}
      <button
        className="control-btn language-toggle segment-vertical"
        onClick={onLanguageToggle}
        title={getTranslation("changeLanguage", language)}
      >
        <div className="language-segment">
          <span className="language-text current">{language === 'ar' ? 'ع' : 'E'}</span>
          <div className="language-divider"></div>
          <span className="language-text next">{language === 'ar' ? 'En' : 'ع'}</span>
        </div>
      </button>

       <button
        className="control-btn map-style-toggle"
        onClick={handleMapStyleToggle}
        title={getTranslation("changeMapStyle", language)}
      >
        <span className="control-icon">
          {mapStyles[currentStyleIndex]?.icon || <FaMap />}
        </span>
      </button>
      {/* Zoom Controls */}
      {/* <div className="zoom-controls">
        <button
          className="control-btn zoom-btn zoom-in"
          onClick={() => mapRef?.current?.zoomIn()}
          title={getTranslation("zoomIn", language)}
        >
          <span className="control-icon">
            <FaPlus />
          </span>
        </button>
        <button
          className="control-btn zoom-btn zoom-out"
          onClick={() => mapRef?.current?.zoomOut()}
          title={getTranslation("zoomOut", language)}
        >
          <span className="control-icon">
            <FaMinus />
          </span>
        </button>
      </div> */}

      {/* Reset View Button */}
      <button
        className="control-btn reset-view"
        onClick={() => {
          if (mapRef?.current) {
            mapRef.current.flyTo({
              center: [45.0792, 23.8859], // Saudi Arabia center
              zoom: 6,
            });
          }
        }}
        title={getTranslation("resetView", language)}
      >
        <span className="control-icon">
          <img src="/map_controls/Home icon.png" alt="Home" className="control-icon-img" />
        </span>
      </button>
    </div>
  );
};

export default MapControls;
