.map-container {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 500px;
}

.map {
  width: 100%;
  height: 100%;
  min-height: 500px;
  background-color: #f0f0f0;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .map-container {
    min-height: 100vh;
    min-height: 100dvh;
  }

  .map {
    min-height: 100vh;
    min-height: 100dvh;
  }
}

.map-info {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-family: 'Fira Sans', 'Cairo', monospace;
}
