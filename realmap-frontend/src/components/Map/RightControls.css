.right-controls {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .right-controls {
    top: 20%;
    right: 15px;
    transform: translateY(-50%);
    gap: 8px;
    /* Add background and styling to match map controls */
    padding: 8px;
    border-radius: 12px;
    /* border: 1px solid rgba(255, 255, 255, 0.1); */
  }
}

@media (max-width: 480px) {
  .right-controls {
    top: 20%;
    right: 10px;
    transform: translateY(-50%);
    gap: 6px;
  }
}

/* Login <PERSON><PERSON> Styles - Match NorthArrow design */
.right-controls .login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  /* border: 1px solid rgba(255, 255, 255, 0.1); */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 50px;
  background: transparent;
}

.right-controls .login-btn:hover {
  background-color: rgba(31, 41, 55, 1);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(31, 41, 55, 0.3);
}

.right-controls .login-btn:active {
  transform: translateY(0);
}

.right-controls .login-btn .control-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.right-controls .login-btn .control-icon-img {
  width: 40px;
  height: 48px;
  object-fit: contain;
  filter: brightness(0) saturate(100%) invert(100%); /* White color */
}

/* Mobile responsive button sizes */
@media (max-width: 768px) {
  .right-controls .login-btn {
    padding: 6px;
    min-width: 44px;
    border-radius: 6px;
  }

  .right-controls .login-btn .control-icon-img {
    width: 32px;
    height: 38px;
    filter: brightness(0) saturate(100%) invert(100%); /* White color */
  }
}

@media (max-width: 480px) {
  .right-controls .login-btn {
    padding: 4px;
    min-width: 40px;
    border-radius: 4px;
  }

  .right-controls .login-btn .control-icon-img {
    width: 28px;
    height: 34px;
    filter: brightness(0) saturate(100%) invert(100%); /* White color */
  }
}

@media (max-width: 375px) {
  .right-controls .login-btn {
    padding: 3px;
    min-width: 36px;
  }

  .right-controls .login-btn .control-icon-img {
    width: 24px;
    height: 30px;
    filter: brightness(0) saturate(100%) invert(100%); /* White color */
  }
}

/* RTL Support */
.rtl .right-controls {
  right: auto;
  left: 20px;
}

@media (max-width: 768px) {
  .rtl .right-controls {
    left: 15px;
    right: auto;
  }
}

@media (max-width: 480px) {
  .rtl .right-controls {
    left: 10px;
    right: auto;
  }
}

/* Very small screens */
@media (max-width: 375px) {
  .right-controls {
    top: 27%;
    right: 8px;
    transform: translateY(-50%);
  }

  .rtl .right-controls {
    left: 8px;
    right: auto;
    transform: translateY(-50%);
  }

  .right-controls .login-btn {
    min-width: 32px;
    min-height: 32px;
    padding: 4px;
  }

  .right-controls .login-btn .control-icon-img {
    width: 16px;
    height: 16px;
  }
}

/* Safe area support for devices with notches */
@supports (padding: max(0px)) {
  .right-controls {
    top: 27%;
    right: max(20px, env(safe-area-inset-right) + 10px);
    transform: translateY(-50%);
  }

  .rtl .right-controls {
    left: max(20px, env(safe-area-inset-left) + 10px);
    right: auto;
    transform: translateY(-50%);
  }
}
