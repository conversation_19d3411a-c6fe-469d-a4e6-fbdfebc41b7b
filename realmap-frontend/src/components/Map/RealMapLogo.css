.realmap-logo-container {
  position: absolute;
  bottom: 20px;
  end: 0px; /* Position it next to Mapbox attribution */
  z-index: 1000;
  pointer-events: none; /* Allow clicks to pass through */
}

.realmap-logo {
  /* background: rgba(255, 255, 255, 0.95); */
  border-radius: 6px;
  padding: 8px 12px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); */
  /* backdrop-filter: blur(10px); */
  /* border: 1px solid rgba(0, 0, 0, 0.1); */
  transition: all 0.3s ease;
}

.realmap-logo:hover {
  /* background: rgba(255, 255, 255, 1); */
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); */
  transform: translateY(-1px);
}

.realmap-logo svg {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .realmap-logo-container {
    bottom: 20px;
    end: 0px;
  }

  .realmap-logo {
    padding: 6px 8px;
  }

  .realmap-logo svg {
    width: 100px;
    height: 32px;
  }
}

/* Small mobile screens */
@media (max-width: 480px) {
  .realmap-logo-container {
    bottom: 20px;
    end: 0px;
  }

  .realmap-logo {
    padding: 4px 6px;
  }


}

/* RTL Support */
.App.rtl .realmap-logo-container {
  right: auto;
  end: 0px;
}

@media (max-width: 768px) {
  .App.rtl .realmap-logo-container {
    end: 0px;
  }
}

@media (max-width: 480px) {
  .App.rtl .realmap-logo-container {
    end: 0px;
  }
}
