.parcel-card {
  position: absolute;
  width: 320px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  opacity: 0;
  transform: scale(0.8);
  transition: all 0.3s ease;
  z-index: 1000;
  font-family: 'Cairo', 'Fira Sans', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Arabic font support */
.parcel-card[dir="rtl"] {
  font-family: 'Cairo', 'Fira Sans', 'Segoe UI', 'Tahoma', 'Arial', 'Helvetica Neue', sans-serif;
}

.parcel-card.visible {
  opacity: 1;
  transform: scale(1);
}

/* Background Section */
.card-background {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.background-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.3) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.4) 100%
  );
}

/* Project Title */
.project-title {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  z-index: 2;
  text-align: left;
}

.project-title h2 {
  color: white;
  font-size: 24px;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

/* RTL Project Title */
.parcel-card[dir="rtl"] .project-title {
  text-align: right;
}

.parcel-card[dir="rtl"] .project-title h2 {
  direction: rtl;
  text-align: right;
}

/* Developer Logo */
.developer-logo {
  position: absolute;
  top: 15px;
  right: 15px;
  z-index: 3;
}

.logo-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 8px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  /* backdrop-filter: blur(10px); */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.logo-text {
  font-size: 16px;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: #1f2937;
  line-height: 1;
  margin-bottom: 2px;
}

.logo-subtext {
  font-size: 10px;
  font-weight: 500;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  color: #6b7280;
  line-height: 1;
}

.developer-logo-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.logo-text-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Virtual Tour Button */
.virtual-tour-btn {
  position: absolute;
  top: 15px;
  left: 15px;
  z-index: 3;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(10px); */
}

.virtual-tour-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* Share Button */
.share-btn {
  position: absolute;
  top: 15px;
  left: 65px; /* Moved to the right to make space for virtual tour button */
  z-index: 3;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(10px); */
}

.share-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* Parcel Arrow - Points to parcel location on map */
.parcel-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  z-index: 5;
}

.parcel-arrow.top {
  border-width: 0 8px 12px 8px;
  border-color: transparent transparent #1f2937 transparent;
}

.parcel-arrow.bottom {
  border-width: 12px 8px 0 8px;
  border-color: #1f2937 transparent transparent transparent;
}

.parcel-arrow.left {
  border-width: 8px 12px 8px 0;
  border-color: transparent #1f2937 transparent transparent;
}

.parcel-arrow.right {
  border-width: 8px 0 8px 12px;
  border-color: transparent transparent transparent #1f2937;
}

/* Card Content */
.card-content {
  padding: 20px;
}

/* RTL Card Content */
.parcel-card[dir="rtl"] .card-content {
  direction: rtl;
}

/* Status Section */
.status-section {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.status-badge {
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* RTL Status Section */
.parcel-card[dir="rtl"] .status-section {
  justify-content: flex-start;
}

.parcel-card[dir="rtl"] .status-badge {
  text-transform: none;
  letter-spacing: normal;
}

/* Unit Details */
.unit-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.detail-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  flex-shrink: 0;
}

.detail-value {
  font-size: 16px;
  color: #1f2937;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  text-align: end;
}

/* RTL Detail Rows */
.parcel-card[dir="rtl"] .detail-row {
  flex-direction: row-reverse;
}

.parcel-card[dir="rtl"] .detail-label {
  text-align: right;
  margin-left: 10px;
  margin-right: 0;
}

.parcel-card[dir="rtl"] .detail-value {
  text-align: start;
  direction: ltr;
  unicode-bidi: embed;
}

.price-row {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.price-value {
  font-size: 20px;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: #1f2937;
}

/* Reserve Button */
.reserve-btn {
  width: 100%;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px 20px;
  font-size: 16px;
  font-weight: 600;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reserve-btn:hover {
  background: #2563eb;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.reserve-btn:active {
  transform: translateY(0);
}

/* RTL Reserve Button */
.parcel-card[dir="rtl"] .reserve-btn {
  text-transform: none;
  letter-spacing: normal;
}

/* Close Button */
.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  /* backdrop-filter: blur(10px); */
  z-index: 4;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* Additional RTL Support */
.parcel-card[dir="rtl"] .developer-logo {
  right: auto;
  left: 15px;
}

.parcel-card[dir="rtl"] .virtual-tour-btn {
  left: auto;
  right: 15px;
}

.parcel-card[dir="rtl"] .share-btn {
  left: auto;
  right: 65px; /* Moved to the left to make space for virtual tour button in RTL */
}

.parcel-card[dir="rtl"] .close-btn {
  right: auto;
  left: 10px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .parcel-card {
    width: 280px;
    max-width: calc(100vw - 40px);
  }

  .card-background {
    height: 140px;
  }

  .project-title h2 {
    font-size: 20px;
  }

  .card-content {
    padding: 16px;
  }

  .detail-value {
    font-size: 14px;
  }

  .price-value {
    font-size: 18px;
  }

  .reserve-btn {
    padding: 12px 16px;
    font-size: 14px;
  }

  /* RTL Mobile adjustments */
  .parcel-card[dir="rtl"] .detail-label {
    margin-left: 8px;
  }
}
