.parcel-popover {
  position: absolute;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  max-width: 400px;
  min-width: 320px;
  overflow: hidden;
  transform: scale(0.8) translateY(10px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.parcel-popover.visible {
  transform: scale(1) translateY(0);
  opacity: 1;
  pointer-events: auto;
}

.popover-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.popover-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.6s;
}

.popover-header:hover::before {
  left: 100%;
}

.popover-title {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.popover-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  z-index: 1;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.popover-content {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.details-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 12px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 8px;
  border-left: 3px solid #1f2937;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.detail-item .label {
  font-size: 12px;
  color: #64748b;
  font-weight: 600;
  font-family: 'Fira Sans', 'Cairo', sans-serif;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item .value {
  font-size: 14px;
  color: #1e293b;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  word-wrap: break-word;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.section-header {
  grid-column: 1 / -1;
  margin: 16px 0 12px 0;
  padding: 10px 14px;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  border-radius: 6px;
  color: white;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 700;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.section-header:first-child {
  margin-top: 0;
}

.popover-image {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.popover-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Popover arrow */
.popover-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.popover-arrow.top {
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent white transparent;
}

.popover-arrow.bottom {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 8px 8px 0 8px;
  border-color: white transparent transparent transparent;
}

.popover-arrow.left {
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 8px 8px 0;
  border-color: transparent white transparent transparent;
}

.popover-arrow.right {
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent white;
}

/* Custom scrollbar */
.popover-content::-webkit-scrollbar {
  width: 6px;
}

.popover-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.popover-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.popover-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .parcel-popover {
    max-width: 90vw;
    min-width: 280px;
  }

  .popover-header {
    padding: 12px 16px;
  }

  .popover-title h3 {
    font-size: 16px;
  }

  .popover-content {
    padding: 16px;
    max-height: 300px;
  }

  .details-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .detail-item {
    padding: 10px;
  }

  .detail-item .label {
    font-size: 11px;
  }

  .detail-item .value {
    font-size: 13px;
  }

  .section-header {
    margin: 12px 0 8px 0;
    padding: 8px 12px;
  }

  .section-header h4 {
    font-size: 13px;
  }

  .popover-image {
    height: 100px;
  }
}

/* RTL Support */
.rtl .parcel-popover {
  direction: rtl;
}

.rtl .popover-title {
  flex-direction: row-reverse;
}

.rtl .popover-header {
  flex-direction: row-reverse;
}

.rtl .detail-item {
  border-left: none;
  border-right: 3px solid #1f2937;
}
