import React, { useEffect, useRef } from "react";
import "./ParcelCard.css";
import { getTranslation } from "../../utils/translations";
import { useLanguage } from "../../App";

const ParcelCard = ({
  parcelData,
  projectData,
  isVisible,
  onClose,
  position = { x: 0, y: 0 },
  mapContainer,
  parcelMapPosition = null, // Position of the parcel on the map

}) => {
  const cardRef = useRef(null);
  const { language } = useLanguage();

  useEffect(() => {
    if (isVisible && cardRef.current && mapContainer) {
      const card = cardRef.current;
      const containerRect = mapContainer.getBoundingClientRect();
      const cardRect = card.getBoundingClientRect();

      let x = position.x;
      let y = position.y;

      // Adjust position to keep card within map bounds
      if (x + cardRect.width > containerRect.width) {
        x = containerRect.width - cardRect.width - 20;
      }
      if (x < 20) {
        x = 20;
      }
      if (y + cardRect.height > containerRect.height) {
        y = position.y - cardRect.height - 20;
      }
      if (y < 20) {
        y = 20;
      }

      card.style.left = `${x}px`;
      card.style.top = `${y}px`;
    }
  }, [isVisible, position, mapContainer]);

  if (!parcelData || !isVisible) {
    return null;
  }

  const formatPrice = (price) => {
    if (!price) return "N/A";
    const formatted = new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(price);
    // For Arabic, ensure numbers are displayed in Western Arabic numerals for better readability
    return language === "ar" ? formatted.replace(/[\u0660-\u0669]/g, (d) => String.fromCharCode(d.charCodeAt(0) - 1584)) : formatted;
  };

  const formatArea = (area) => {
    if (!area) return "N/A";
    const formatted = new Intl.NumberFormat(language === "ar" ? "ar-SA" : "en-US").format(area);
    // For Arabic, ensure numbers are displayed in Western Arabic numerals for better readability
    return language === "ar" ? formatted.replace(/[\u0660-\u0669]/g, (d) => String.fromCharCode(d.charCodeAt(0) - 1584)) : formatted;
  };

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return "#22c55e";
      case "reserved":
        return "#f59e0b";
      case "sold":
        return "#ef4444";
      default:
        return "#6b7280";
    }
  };

  const getStatusText = (status) => {
    switch (status?.toLowerCase()) {
      case "available":
        return getTranslation("available", language);
      case "reserved":
        return getTranslation("reserved", language);
      case "sold":
        return getTranslation("sold", language);
      default:
        return status || "N/A";
    }
  };

  const handleReserveClick = () => {
    // Handle reservation logic here
    console.log("Reserve unit clicked for parcel:", parcelData.parcel_number);
  };

  const handleShareClick = () => {
    // Handle share logic here
    console.log("Share clicked for parcel:", parcelData.parcel_number);
  };

  const handleVirtualTourClick = () => {
    // Handle virtual tour logic here
    console.log("Virtual tour clicked for parcel:", parcelData.parcel_number);
  };

  // Calculate arrow direction and position
  const getArrowDirection = () => {
    if (!parcelMapPosition || !cardRef.current || !mapContainer) {
      return { direction: 'bottom', style: {} };
    }

    const cardRect = cardRef.current.getBoundingClientRect();
    const mapRect = mapContainer.getBoundingClientRect();

    // Get card center relative to map
    const cardCenterX = cardRect.left + cardRect.width / 2 - mapRect.left;
    const cardCenterY = cardRect.top + cardRect.height / 2 - mapRect.top;

    // Calculate direction from card to parcel
    const deltaX = parcelMapPosition.x - cardCenterX;
    const deltaY = parcelMapPosition.y - cardCenterY;

    // Determine which side of the card the arrow should be on
    const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI);

    let direction = 'bottom';
    let arrowStyle = {};

    if (angle >= -45 && angle < 45) {
      // Right side
      direction = 'right';
      arrowStyle = {
        top: '50%',
        right: '-8px',
        transform: 'translateY(-50%)'
      };
    } else if (angle >= 45 && angle < 135) {
      // Bottom side
      direction = 'bottom';
      arrowStyle = {
        bottom: '-8px',
        left: '50%',
        transform: 'translateX(-50%)'
      };
    } else if (angle >= 135 || angle < -135) {
      // Left side
      direction = 'left';
      arrowStyle = {
        top: '50%',
        left: '-8px',
        transform: 'translateY(-50%)'
      };
    } else {
      // Top side
      direction = 'top';
      arrowStyle = {
        top: '-8px',
        left: '50%',
        transform: 'translateX(-50%)'
      };
    }

    return { direction, style: arrowStyle };
  };

  const arrowInfo = getArrowDirection();

  return (
    <div
      ref={cardRef}
      className={`parcel-card ${isVisible ? "visible" : ""}`}
      dir={language === "ar" ? "rtl" : "ltr"}
    >
      {/* Arrow pointing to parcel location */}
      {parcelMapPosition && (
        <div
          className={`parcel-arrow ${arrowInfo.direction}`}
          style={arrowInfo.style}
        />
      )}
      {/* Background Image */}
      <div className="card-background">
        {parcelData.images && parcelData.images.length > 0 ? (
          <img
            src={typeof parcelData.images[0] === 'string' ? parcelData.images[0] : parcelData.images[0]?.url}
            alt="Project background"
            className="background-image"
          />
        ) : (
          <div className="default-background">
  <img
            src={projectData?.default_unit_image_url}
            alt="Project background"
            className="background-image"
          />

          </div>
        )}
        
        {/* Overlay */}
        <div className="background-overlay"></div>
        
        {/* Project Title */}
        {/* <div className="project-title">
          <h2>{projectData?.name || "مشروع رأي الخرج"}</h2>
        </div>
         */}
        {/* Developer Logo */}
        <div className="developer-logo">
          {projectData?.developer_logo_url ? (
            <div className="logo-container">
              <img
                src={projectData.developer_logo_url}
                alt={projectData.developer_name || "Developer Logo"}
                className="developer-logo-image"
                onError={(e) => {
                  // Fallback to text logo if image fails to load
                  e.target.style.display = 'none';
                  e.target.nextSibling.style.display = 'flex';
                }}
              />
              <div className="logo-text-fallback" style={{ display: 'none' }}>
                <span className="logo-text">{projectData.developer_name || "أصل"}</span>
              </div>
            </div>
          ) : (
            <div className="logo-container">
              {/* <span className="logo-text">{projectData?.developer_name || "أصل"}</span> */}
              {/* <span className="logo-subtext">ASSL</span> */}
            </div>
          )}
        </div>

        {/* Virtual Tour Button */}
        <button className="virtual-tour-btn" onClick={handleVirtualTourClick} title={getTranslation("virtualTour", language)}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" fill="none"/>
            <path d="M12 2a10 10 0 0 1 0 20" stroke="currentColor" strokeWidth="1"/>
            <path d="M2 12h20" stroke="currentColor" strokeWidth="1"/>
            <path d="M12 2c-2.5 3-2.5 7 0 10s2.5 7 0 10" stroke="currentColor" strokeWidth="1"/>
            <path d="M12 2c2.5 3 2.5 7 0 10s-2.5 7 0 10" stroke="currentColor" strokeWidth="1"/>
            <text x="12" y="16" textAnchor="middle" fontSize="8" fill="currentColor">360°</text>
          </svg>
        </button>

        {/* Share Button */}
        <button className="share-btn" onClick={handleShareClick}>
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z" fill="currentColor"/>
          </svg>
        </button>
      </div>

      {/* Card Content */}
      <div className="card-content">
        {/* Status Badge */}
        <div className="status-section">
          <div 
            className="status-badge"
            style={{ backgroundColor: getStatusColor(parcelData.sales_status) }}
          >
            {getStatusText(parcelData.sales_status)}
          </div>
        </div>

        {/* Unit Details */}
        <div className="unit-details">
          <div className="detail-row">
            <span className="detail-label">
              {getTranslation("unitNumber", language)}:
            </span>
            <span className="detail-value" dir="ltr">{parcelData.parcel_number}</span>
          </div>

          <div className="detail-row">
            <span className="detail-label">
              {getTranslation("blockNumber", language)}:
            </span>
            <span className="detail-value" dir="ltr">{parcelData.block_number || "23"}</span>
          </div>
          
          <div className="detail-row">
            <span className="detail-label">
              {getTranslation("areaLabel", language)}:
            </span>
            <span className="detail-value" dir="ltr">
              {formatArea(parcelData.area)} {getTranslation("sqm", language)}
            </span>
          </div>

          <div className="detail-row price-row">
            <span className="detail-label">
              {getTranslation("priceLabel", language)}:
            </span>
            <span className="detail-value price-value" dir="ltr">
              {formatPrice(parcelData.total_price || 200000)} {getTranslation("sar", language)}
            </span>
          </div>
        </div>

        {/* Reserve Button - Only show if unit is available */}
        {parcelData.sales_status?.toLowerCase() === "available" && (
          <button className="reserve-btn" onClick={handleReserveClick}>
            {getTranslation("reserveUnit", language)}
          </button>
        )}
      </div>

      {/* Close Button */}
      <button className="close-btn" onClick={onClose}>
        ✕
      </button>
    </div>
  );
};

export default ParcelCard;
