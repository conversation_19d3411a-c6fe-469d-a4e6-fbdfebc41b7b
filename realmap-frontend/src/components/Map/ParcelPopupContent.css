/* Parcel Popup Content Styles */
.parcel-popup-content {
  width: 280px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  color: #1e293b;
}

/* Header */
.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 10px;
}

.popup-title h3 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.status-badge {
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 6px;
  flex-shrink: 0;
}

.virtual-tour-btn,
.share-btn {
  background: rgba(31, 41, 55, 0.8);
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.virtual-tour-btn:hover,
.share-btn:hover {
  background: rgba(31, 41, 55, 1);
  transform: scale(1.1);
}

/* Image */
.popup-image {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.popup-image img[src=""],
.popup-image img:not([src]) {
  opacity: 0;
}

/* Details */
.popup-details {
  margin-bottom: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #e5e7eb;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.detail-value {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
}

.price-value {
  color: #059669;
  font-weight: 700;
}

/* Reserve Button */
.reserve-btn {
  width: 100%;
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
}

.reserve-btn:hover {
  background: linear-gradient(135deg, #047857 0%, #065f46 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

/* RTL Support */
.parcel-popup-content.rtl {
  direction: rtl;
}

.parcel-popup-content.rtl .popup-header {
  flex-direction: row-reverse;
}

.parcel-popup-content.rtl .action-buttons {
  order: -1;
}

.parcel-popup-content.rtl .detail-row {
  flex-direction: row-reverse;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .parcel-popup-content {
    width: 260px;
  }
  
  .popup-title h3 {
    font-size: 14px;
  }
  
  .popup-image {
    height: 100px;
  }
  
  .virtual-tour-btn,
  .share-btn {
    width: 28px;
    height: 28px;
  }
  
  .virtual-tour-btn svg,
  .share-btn svg {
    width: 16px;
    height: 16px;
  }
  
  .detail-label,
  .detail-value {
    font-size: 11px;
  }
  
  .reserve-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* Mapbox Popup Customization */
.mapboxgl-popup-content {
  padding: 16px !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

.mapboxgl-popup-close-button {
  font-size: 18px !important;
  color: #64748b !important;
  padding: 4px !important;
  width: 24px !important;
  height: 24px !important;
  line-height: 16px !important;
}

.mapboxgl-popup-close-button:hover {
  background: rgba(0, 0, 0, 0.1) !important;
  color: #1f2937 !important;
}

.mapboxgl-popup-tip {
  border-top-color: white !important;
  border-bottom-color: white !important;
  border-left-color: white !important;
  border-right-color: white !important;
}
