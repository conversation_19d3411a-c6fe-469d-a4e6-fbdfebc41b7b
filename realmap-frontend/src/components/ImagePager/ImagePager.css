.image-pager {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
}

.image-container {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
  min-height: 150px;
}

.main-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.main-image.loading {
  opacity: 0;
}

.image-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2;
}

.loading-spinner {
  position: relative;
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background-image: url('/logo_only.png');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  z-index: 1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.nav-button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-button:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
}

.nav-button:active {
  transform: translateY(-50%) scale(0.95);
}

.nav-button.prev {
  left: 10px;
}

.nav-button.next {
  right: 10px;
}

.image-counter {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 3;
}

.dots-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px;
  gap: 8px;
  background: white;
  min-height: 40px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: none;
  background: #ddd;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dot:hover {
  background: #bbb;
  transform: scale(1.2);
}

.dot.active {
  background: #1f2937;
  transform: scale(1.3);
}

/* No images state */
.no-images {
  background: #f8f9fa;
  border: 2px dashed #ddd;
}

.placeholder-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
  text-align: center;
  padding: 20px;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.placeholder-image p {
  margin: 0;
  font-size: 14px;
  font-family: 'Cairo', 'Fira Sans', sans-serif;
  font-weight: 400;
  color: #666;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .nav-button {
    width: 36px;
    height: 36px;
    font-size: 18px;
  }
  
  .nav-button.prev {
    left: 8px;
  }
  
  .nav-button.next {
    right: 8px;
  }
  
  .image-counter {
    top: 8px;
    right: 8px;
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .dots-container {
    padding: 8px;
    gap: 6px;
  }
  
  .dot {
    width: 6px;
    height: 6px;
  }
  
  .placeholder-icon {
    font-size: 36px;
  }
  
  .placeholder-image p {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .nav-button {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .nav-button.prev {
    left: 6px;
  }
  
  .nav-button.next {
    right: 6px;
  }
  
  .image-counter {
    font-size: 10px;
    padding: 2px 5px;
  }
  
  .dots-container {
    padding: 6px;
    gap: 4px;
  }
}

/* RTL Support */
.rtl .nav-button.prev {
  left: auto;
  right: 10px;
}

.rtl .nav-button.next {
  right: auto;
  left: 10px;
}

.rtl .image-counter {
  right: auto;
  left: 10px;
}

@media (max-width: 768px) {
  .rtl .nav-button.prev {
    right: 8px;
  }
  
  .rtl .nav-button.next {
    left: 8px;
  }
  
  .rtl .image-counter {
    left: 8px;
  }
}

@media (max-width: 480px) {
  .rtl .nav-button.prev {
    right: 6px;
  }
  
  .rtl .nav-button.next {
    left: 6px;
  }
}

/* Touch gestures for mobile */
@media (max-width: 768px) {
  .image-container {
    touch-action: pan-y;
  }
  
  .main-image {
    user-select: none;
    -webkit-user-select: none;
    -webkit-touch-callout: none;
  }
}

/* Smooth transitions */
.image-pager * {
  box-sizing: border-box;
}

/* Focus states for accessibility */
.nav-button:focus,
.dot:focus {
  outline: 2px solid #1f2937;
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-button {
    background: rgba(0, 0, 0, 0.8);
    border: 1px solid white;
  }
  
  .dot {
    border: 1px solid #333;
  }
  
  .dot.active {
    background: #000;
    border-color: #000;
  }
}
