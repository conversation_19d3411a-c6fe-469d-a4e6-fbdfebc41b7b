import React, { useState, useEffect } from 'react';
import './ImagePager.css';

const ImagePager = ({ images = [], alt = 'Image', className = '' }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [loadedImages, setLoadedImages] = useState(new Set());

  // Reset when images change
  useEffect(() => {
    setCurrentIndex(0);
    setLoadedImages(new Set());
    setIsLoading(true);
  }, [images]);

  // Preload current and next images
  useEffect(() => {
    if (images.length > 0) {
      const imagesToPreload = [
        currentIndex,
        (currentIndex + 1) % images.length,
        currentIndex > 0 ? currentIndex - 1 : images.length - 1
      ];

      imagesToPreload.forEach(index => {
        if (images[index] && !loadedImages.has(index)) {
          const img = new Image();
          img.onload = () => {
            setLoadedImages(prev => new Set([...prev, index]));
            if (index === currentIndex) {
              setIsLoading(false);
            }
          };
          img.onerror = () => {
            if (index === currentIndex) {
              setIsLoading(false);
            }
          };
          img.src = typeof images[index] === 'string' ? images[index] : images[index].url;
        }
      });
    }
  }, [currentIndex, images, loadedImages]);

  if (!images || images.length === 0) {
    return (
      <div className={`image-pager no-images ${className}`}>
        <div className="placeholder-image">
          <div className="placeholder-icon">📷</div>
          <p>لا توجد صور متاحة</p>
        </div>
      </div>
    );
  }

  const goToNext = () => {
    setCurrentIndex((prev) => (prev + 1) % images.length);
  };

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);
  };

  const goToSlide = (index) => {
    setCurrentIndex(index);
  };

  const currentImage = images[currentIndex];
  const imageUrl = typeof currentImage === 'string' ? currentImage : currentImage?.url;
  const imageAlt = typeof currentImage === 'string' ? alt : currentImage?.alt_text || alt;

  return (
    <div className={`image-pager ${className}`}>
      <div className="image-container">
        {isLoading && (
          <div className="image-loading">
            <div className="loading-spinner"></div>
          </div>
        )}
        
        <img
          src={imageUrl}
          alt={imageAlt}
          className={`main-image ${isLoading ? 'loading' : ''}`}
          onLoad={() => setIsLoading(false)}
          onError={(e) => {
            setIsLoading(false);
            e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDMwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0xMjAgODBIMTgwVjEyMEgxMjBWODBaIiBmaWxsPSIjREREIi8+CjxjaXJjbGUgY3g9IjE0MCIgY3k9IjkwIiByPSI1IiBmaWxsPSIjQkJCIi8+CjxwYXRoIGQ9Ik0xMzAgMTEwTDE1MCA5MEwxNzAgMTEwSDE3MFYxMjBIMTMwVjExMFoiIGZpbGw9IiNCQkIiLz4KPHRleHQgeD0iMTUwIiB5PSIxNTAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiM5OTkiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCI+UGFyY2VsIEltYWdlPC90ZXh0Pgo8L3N2Zz4K';
          }}
        />

        {images.length > 1 && (
          <>
            <button
              className="nav-button prev"
              onClick={goToPrevious}
              aria-label="Previous image"
            >
              ‹
            </button>
            <button
              className="nav-button next"
              onClick={goToNext}
              aria-label="Next image"
            >
              ›
            </button>
          </>
        )}

        {images.length > 1 && (
          <div className="image-counter">
            {currentIndex + 1} / {images.length}
          </div>
        )}
      </div>

      {images.length > 1 && (
        <div className="dots-container">
          {images.map((_, index) => (
            <button
              key={index}
              className={`dot ${index === currentIndex ? 'active' : ''}`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to image ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default ImagePager;
