import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaTimes } from 'react-icons/fa';
import { getTranslation } from '../../utils/translations';
import { useLanguage } from '../../App';
import TermsModal from './TermsModal';
import './Terms.css';

const TermsNotification = () => {
  const { language } = useLanguage();
  const [isVisible, setIsVisible] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    // Check if user has already accepted terms
    const hasAcceptedTerms = localStorage.getItem('hasAcceptedTerms');
    if (!hasAcceptedTerms) {
      // Show notification after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, []);

  const handleClose = () => {
    setIsVisible(false);
    // Mark as accepted in localStorage
    localStorage.setItem('hasAcceptedTerms', 'true');
  };

  const handleReadTerms = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    handleClose(); // Also close the notification when modal is closed
  };

  if (!isVisible) return null;

  return (
    <>
      <div className={`terms-notification ${language === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="terms-notification-content">
          <div className="terms-icon">
            <FaExclamationTriangle />
          </div>
          
          <div className="terms-text">
            <h4 className="terms-title">
              {getTranslation('termsNotificationTitle', language)}
            </h4>
            <p className="terms-message">
              {getTranslation('termsNotificationMessage', language)}
            </p>
          </div>

          <div className="terms-actions">
            <button 
              className="terms-read-btn"
              onClick={handleReadTerms}
            >
              {getTranslation('readTerms', language)}
            </button>
            
            <button 
              className="terms-close-btn"
              onClick={handleClose}
            >
              <FaTimes />
            </button>
          </div>
        </div>
      </div>

      <TermsModal 
        isOpen={isModalOpen}
        onClose={handleModalClose}
        language={language}
      />
    </>
  );
};

export default TermsNotification;
