import React, { useEffect } from 'react';
import { FaExclamationTriangle, FaTimes } from 'react-icons/fa';
import { getTranslation } from '../../utils/translations';
import './Terms.css';

const TermsModal = ({ isOpen, onClose, language }) => {
  useEffect(() => {
    if (isOpen) {
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div 
      className="terms-modal-overlay" 
      onClick={handleOverlayClick}
    >
      <div className={`terms-modal ${language === 'ar' ? 'rtl' : 'ltr'}`}>
        <div className="terms-modal-header">
          <div className="terms-modal-title">
            <FaExclamationTriangle className="terms-modal-icon" />
            <h2>{getTranslation('termsModalTitle', language)}</h2>
          </div>
          <button 
            className="terms-modal-close-btn"
            onClick={onClose}
          >
            <FaTimes />
          </button>
        </div>

        <div className="terms-modal-content">
          <div className="terms-modal-text">
            <p>{getTranslation('termsContent', language)}</p>
          </div>
        </div>

        <div className="terms-modal-footer">
          <button 
            className="terms-modal-close-footer-btn"
            onClick={onClose}
          >
            {getTranslation('closeTerms', language)}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TermsModal;
