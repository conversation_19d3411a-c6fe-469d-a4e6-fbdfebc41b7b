/* Terms Notification Styles */
.terms-notification {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 16px 20px;
  max-width: 90%;
  width: auto;
  min-width: 320px;
  z-index: 1000;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.terms-notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.terms-icon {
  color: #fbbf24;
  font-size: 20px;
  flex-shrink: 0;
}

.terms-text {
  flex: 1;
  color: white;
}

.terms-title {
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #fbbf24;
}

.terms-message {
  font-size: 12px;
  margin: 0;
  line-height: 1.4;
  opacity: 0.9;
}

.terms-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.terms-read-btn {
  background: #374151;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.terms-read-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.terms-close-btn {
  background: transparent;
  color: #9ca3af;
  border: none;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.terms-close-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

/* RTL Support for Notification */
.terms-notification.rtl .terms-notification-content {
  direction: rtl;
}

.terms-notification.rtl .terms-text {
  text-align: right;
}

/* Terms Modal Styles */
.terms-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.terms-modal {
  background: #1f2937;
  border-radius: 16px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.terms-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 24px 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.terms-modal-title {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.terms-modal-icon {
  color: #fbbf24;
  font-size: 24px;
}

.terms-modal-title h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.terms-modal-close-btn {
  background: transparent;
  color: #9ca3af;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 18px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.terms-modal-close-btn:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.terms-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.terms-modal-text {
  color: #d1d5db;
  line-height: 1.6;
  font-size: 14px;
}

.terms-modal-text p {
  margin: 0;
}

.terms-modal-footer {
  padding: 16px 24px 24px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: center;
}

.terms-modal-close-footer-btn {
  background: #374151;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.terms-modal-close-footer-btn:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

/* RTL Support for Modal */
.terms-modal.rtl {
  direction: rtl;
}

.terms-modal.rtl .terms-modal-text {
  text-align: right;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .terms-notification {
    bottom: 10px;
    left: 10px;
    right: 10px;
    transform: none;
    max-width: none;
    min-width: auto;
  }

  .terms-notification-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .terms-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  .terms-read-btn {
    flex: 1;
    margin-right: 8px;
  }

  .terms-modal {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .terms-modal-header {
    padding: 20px 20px 12px 20px;
  }

  .terms-modal-title h2 {
    font-size: 18px;
  }

  .terms-modal-content {
    padding: 20px;
  }

  .terms-modal-footer {
    padding: 12px 20px 20px 20px;
  }
}
