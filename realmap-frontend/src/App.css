* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body,
html,
#root {
  height: 100%;
  width: 100%;
  font-family: 'Cairo', 'Fira Sans', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  font-weight: 400;
}

.App {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  position: relative;
}

/* RTL Support */
.App.rtl {
  direction: rtl;
}

.App.ltr {
  direction: ltr;
}

/* Shared View Banner */
.shared-view-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  position: relative;
}

.shared-view-banner span {
  display: flex;
  align-items: center;
  gap: 8px;
}

.exit-shared-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exit-shared-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.main-content {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100vh;
}

.map-wrapper {
  height: 100vh;
  position: relative;
  width: 100%;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .App {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile browsers */
  }

  .main-content {
    height: 100vh;
    height: 100dvh;
  }

  .map-wrapper {
    height: 100vh;
    height: 100dvh;
  }
}
