/* Mobile Optimizations for Real Estate Map */

/* Prevent zoom on input focus for iOS */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  select,
  textarea,
  input[type="text"],
  input[type="password"],
  input[type="datetime"],
  input[type="datetime-local"],
  input[type="date"],
  input[type="month"],
  input[type="time"],
  input[type="week"],
  input[type="number"],
  input[type="email"],
  input[type="url"],
  input[type="search"],
  input[type="tel"],
  input[type="color"] {
    font-size: 16px !important;
  }
}

/* Touch-friendly interactions */
@media (max-width: 768px) {
  /* Increase touch targets */
  button,
  .control-btn,
  .status-btn,
  .filters-toggle-btn {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }
  
  /* Improve scrolling */
  .map-filters-container,
  .filters-content {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  /* Prevent text selection on UI elements */
  .header,
  .map-controls,
  .map-filters-container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
  
  /* Allow text selection in inputs */
  input,
  textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .header {
    padding: 4px 8px;
    top: 8px;
    max-width: 300px;
  }

  .map-controls {
    top: 50%;
    transform: translateY(-50%);
  }

  .map-filters-overlay {
    padding-top: 40px;
  }

  .map-filters-container {
    max-height: calc(100vh - 60px);
  }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
  .header {
    padding: 4px 8px;
    top: 10px;
    max-width: 280px;
    border-radius: 4px;
  }

  .project-name {
    font-size: 11px;
  }

  .search-input {
    padding: 5px 24px 5px 6px;
    font-size: 15px;
    min-height: 30px;
  }

  .search-icon {
    right: 5px;
    font-size: 10px;
  }

  .filters-toggle-btn {
    min-width: 30px;
    height: 30px;
    padding: 3px;
  }

  .map-controls {
    top: 50%;
    left: 8px;
    transform: translateY(-50%);
    gap: 4px;
    padding: 4px;
  }

  .control-btn {
    min-width: 32px;
    min-height: 32px;
    padding: 4px;
    font-size: 10px;
  }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .header,
  .map-controls,
  .map-filters-container {
    /* backdrop-filter: blur(20px); */
  }
}

/* Dark mode support for mobile */
@media (prefers-color-scheme: dark) {
  .header {
    /* background-color: rgba(17, 24, 39, 0.95); */
    border-color: rgba(255, 255, 255, 0.15);
  }
  
  .search-input {
    background: rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .header,
  .control-btn,
  .status-btn,
  .map-filters-container {
    transition: none;
    animation: none;
  }
}

/* Focus styles for accessibility */
@media (max-width: 768px) {
  .search-input:focus,
  .control-btn:focus,
  .filters-toggle-btn:focus,
  .status-btn:focus {
    outline: 2px solid #10b981;
    outline-offset: 2px;
  }
}

/* Safe area insets for devices with notches */
@supports (padding: max(0px)) {
  .header {
    padding-left: max(8px, env(safe-area-inset-left));
    padding-right: max(8px, env(safe-area-inset-right));
    top: max(20px, env(safe-area-inset-top) + 10px);
  }

  .right-controls {
    top: 50%;
    right: max(20px, env(safe-area-inset-right) + 10px);
    transform: translateY(-50%);
  }

  .rtl .right-controls {
    left: max(20px, env(safe-area-inset-left) + 10px);
    right: auto;
    transform: translateY(-50%);
  }

  .map-controls {
    left: max(15px, env(safe-area-inset-left) + 5px);
  }

  .rtl .map-controls {
    right: max(15px, env(safe-area-inset-right) + 5px);
    left: auto;
  }
}

/* Prevent overlapping elements */
@media (max-width: 768px) {
  .header {
    z-index: 1002;
  }

  .right-controls {
    z-index: 1001;
  }

  .map-controls {
    z-index: 1001;
  }

  .map-filters-overlay {
    z-index: 1003;
  }
}

/* Improve performance on mobile */
@media (max-width: 768px) {
  .header,
  .map-controls,
  .map-filters-container {
    will-change: transform;
    transform: translateZ(0);
  }
}
