# RealMap - Real Estate Mapping System

A modern, responsive real estate mapping application built with React and Mapbox GL JS, designed specifically for the Saudi Arabian market.

## 🌟 Features

### 🗺️ Interactive Mapping
- **Dynamic Map Styles**: Switch between Streets, Satellite, and Outdoors views
- **Project Visualization**: Display real estate projects with custom markers
- **Block & Parcel Management**: Interactive visualization of property blocks and parcels
- **Zoom-based Visibility**: Smart layer management based on zoom levels
- **Mobile-Optimized Controls**: Touch-friendly map interactions

### 🌐 Multilingual Support
- **Arabic & English**: Full RTL (Right-to-Left) support for Arabic
- **Dynamic Language Switching**: Instant language toggle without page reload
- **Localized Content**: All UI elements and messages translated

### 📱 Responsive Design
- **Mobile-First Approach**: Optimized for smartphones and tablets
- **Collapsible Sidebar**: Space-efficient design with smooth animations
- **Touch-Friendly Interface**: 44px minimum touch targets for mobile
- **Adaptive Layout**: Seamless experience across all screen sizes

### 🎛️ Advanced Controls
- **Map Style Toggle**: Easy switching between map types
- **Sidebar Management**: Show/hide sidebar with smooth transitions
- **Zoom Controls**: Custom zoom in/out buttons
- **Reset View**: Quick return to default map view
- **Language Toggle**: Instant Arabic/English switching

### 🏗️ Architecture
- **Error Boundaries**: Graceful error handling and recovery
- **Loading States**: Progressive loading with visual feedback
- **Performance Optimized**: Efficient data loading and rendering
- **Modular Components**: Clean, maintainable code structure

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Mapbox access token

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd realmap-frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Environment Variables**
   - Copy `.env.example` to `.env`
   - Update the API URL and Mapbox token as needed
   ```bash
   cp .env.example .env
   ```
   - Edit `.env` file with your configuration:
   ```
   REACT_APP_API_URL=http://localhost:8001
   REACT_APP_MAPBOX_TOKEN=your_mapbox_token_here
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Open your browser**
   - Navigate to `http://localhost:3000`

## 🏗️ Project Structure

```
src/
├── components/
│   ├── ErrorBoundary/     # Error handling components
│   ├── Header/            # Top navigation bar
│   ├── LoadingSpinner/    # Loading indicators
│   ├── Map/               # Map-related components
│   │   ├── MapControls.js # Map control buttons
│   │   ├── OptimizedMap.js# Main map component
│   │   └── LoadingOverlay.js # Map loading overlay
│   ├── ProjectSelector/   # Project selection dropdown
│   └── Sidebar/           # Collapsible sidebar
├── services/              # API service functions
├── utils/                 # Utility functions and translations
└── App.js                 # Main application component
```

## 🎨 Styling & Theming

### CSS Architecture
- **Component-based styles**: Each component has its own CSS file
- **Responsive breakpoints**: Mobile (768px), Tablet (1024px), Desktop (1200px+)
- **CSS Variables**: Consistent color scheme and spacing
- **RTL Support**: Full right-to-left layout support

### Key Breakpoints
- **Mobile**: ≤ 768px
- **Tablet**: 769px - 1024px
- **Desktop**: ≥ 1025px
- **Large Desktop**: ≥ 1200px

## 🌍 Internationalization

### Supported Languages
- **Arabic (ar)**: Primary language with RTL support
- **English (en)**: Secondary language with LTR support

### Translation System
- Centralized translation management in `src/utils/translations.js`
- Context-based language switching
- Dynamic text direction (RTL/LTR)

## 📱 Mobile Optimization

### Touch Interactions
- **Minimum 44px touch targets**: Ensures accessibility
- **Touch-friendly gestures**: Optimized for mobile map interaction
- **Prevent zoom on input**: iOS-specific optimizations

### Performance
- **Lazy loading**: Components loaded on demand
- **Optimized rendering**: Efficient map layer management
- **Smooth animations**: Hardware-accelerated transitions

## 🔧 Configuration

### Map Settings
```javascript
// Mobile-optimized map configuration
{
  touchZoomRotate: true,
  touchPitch: false,
  dragRotate: false,
  pitchWithRotate: false,
  dragPan: true,
  keyboard: true,
  doubleClickZoom: true,
  scrollZoom: true
}
```

### Viewport Configuration
```html
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
```

## 🚀 Deployment

### Build for Production
```bash
npm run build
```

### Environment Variables
- `REACT_APP_API_BASE_URL`: Backend API URL
- `REACT_APP_MAPBOX_TOKEN`: Mapbox access token

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on mobile and desktop
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team

---

**Built with ❤️ for the Saudi Arabian real estate market**
