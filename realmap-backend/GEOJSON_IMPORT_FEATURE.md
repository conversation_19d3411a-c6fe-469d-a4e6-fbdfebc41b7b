# GeoJSON Import Feature - FIXED VERSION

## الوصف

تم تحديث لوحة التحكم لتدعم رفع ملفات GeoJSON عند إنشاء مشروع جديد. الملفات يتم حفظها مؤقتاً، ثم قراءة البيانات منها وإستيرادها إلى قاعدة البيانات، ثم حذف الملفات المؤقتة.

## الميزات المضافة

### 1. رفع الملفات

-   إمكانية رفع ملف `Blocks.geojson` لبيانات البلوكات
-   إمكانية رفع ملف `Parcels.geojson` لبيانات القطع
-   الملفات لا يتم حفظها - فقط قراءة البيانات منها
-   حد أقصى 10MB لكل ملف

### 2. الاستيراد التلقائي

-   استيراد تلقائي للبيانات بعد إنشاء المشروع
-   إنشاء البلوكات والقطع في قاعدة البيانات
-   ربط تلقائي بين القطع والبلوكات
-   إشعارات للمستخدم بحالة الاستيراد

### 3. معالجة الأخطاء

-   التحقق من صحة تنسيق GeoJSON
-   معالجة الأخطاء مع رسائل واضحة
-   تسجيل الأخطاء في ملف اللوج
-   استمرار الاستيراد حتى لو فشل بعض العناصر

## كيفية الاستخدام

### 1. إنشاء مشروع جديد

1. اذهب إلى لوحة التحكم
2. انقر على "Projects" → "Create"
3. املأ معلومات المشروع الأساسية
4. في قسم "GeoJSON Files":
    - ارفع ملف `Blocks.geojson` (اختياري)
    - ارفع ملف `Parcels.geojson` (اختياري)
5. انقر "Create"
6. ستظهر إشعارات بحالة الاستيراد

### 2. تنسيق الملفات المطلوب

#### ملف البلوكات (Blocks.geojson):

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": { ... },
      "properties": {
        "BlockNo": "A1",
        "Shape_Length": 400.0,
        "Shape_Area": 10000.0,
        "Centroid_X": 39.97877,
        "Centroid_Y": 21.46502
      }
    }
  ]
}
```

#### ملف القطع (Parcels.geojson):

```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": { ... },
      "properties": {
        "Parcel_No": "P001",
        "Block_No": "A1",
        "total_area": "984 sqm",
        "price_per_sqm": "1500 AED",
        "price_with_tax": "1476000 AED",
        "sales_status": "Available",
        "property_type": "Residential",
        "usage_type": "Villa"
      }
    }
  ]
}
```

## الملفات المعدلة/المضافة

### ملفات جديدة:

-   `app/Services/GeoJsonImportService.php` - خدمة استيراد ملفات GeoJSON

### ملفات معدلة:

-   `app/Filament/Resources/RealEstateProjectResource.php` - إضافة حقول رفع الملفات
-   `app/Filament/Resources/RealEstateProjectResource/Pages/CreateRealEstateProject.php` - معالجة الملفات المرفوعة

## الخصائص التقنية

### 1. مرونة في أسماء الخصائص

النظام يدعم أسماء مختلفة للخصائص:

-   `BlockNo`, `Block_No`, `block_number`, `BLOCK_NO`
-   `Parcel_No`, `parcel_number`, `PARCEL_NO`
-   `Shape_Length`, `shape_length`
-   `land_status`, `sales_status`

### 2. استخراج البيانات الذكي

-   استخراج الأرقام من النصوص (مثل "984 sqm" → 984)
-   تحويل التواريخ تلقائياً
-   قيم افتراضية للحقول المفقودة

### 3. الأمان

-   التحقق من صحة تنسيق JSON
-   التحقق من وجود الخصائص المطلوبة
-   معالجة الاستثناءات
-   تسجيل الأخطاء

## اختبار النظام

يمكنك اختبار النظام باستخدام الملفات الموجودة في:

-   `oldWEBapp/public/Blocks.geojson`
-   `oldWEBapp/public/Parcels.geojson`

## ملاحظات مهمة

1. **ترتيب الرفع**: يُفضل رفع ملف البلوكات أولاً، ثم ملف القطع
2. **الربط التلقائي**: القطع تُربط بالبلوكات بناءً على رقم البلوك
3. **عدم حفظ الملفات**: الملفات لا يتم حفظها في الخادم
4. **الإشعارات**: ستظهر إشعارات منفصلة لكل ملف + إشعار ملخص
5. **معالجة الأخطاء**: النظام يستمر في الاستيراد حتى لو فشل بعض العناصر
