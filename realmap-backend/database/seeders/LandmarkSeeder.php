<?php

namespace Database\Seeders;

use App\Models\Landmark;
use App\Models\RealEstateProject;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class LandmarkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first project for demo purposes
        $project = RealEstateProject::first();

        if (!$project) {
            $this->command->warn('No projects found. Please run RealEstateProjectSeeder first.');
            return;
        }

        $landmarks = [
            [
                'name' => 'مسجد الحرم الشريف',
                'type' => 'mosque',
                'project_id' => $project->id,
                'latitude' => 21.4225,
                'longitude' => 39.8262,
                'description' => 'المسجد الحرام في مكة المكرمة',
                'is_active' => true,
                'min_zoom' => 14,
                'max_zoom' => 22,
                'display_order' => 1,
            ],
            [
                'name' => 'مدرسة الملك عبدالعزيز',
                'type' => 'school',
                'project_id' => $project->id,
                'latitude' => 21.4267,
                'longitude' => 39.8278,
                'description' => 'مدرسة ابتدائية ومتوسطة وثانوية',
                'is_active' => true,
                'min_zoom' => 14,
                'max_zoom' => 22,
                'display_order' => 2,
            ],
            [
                'name' => 'مستشفى الملك فيصل',
                'type' => 'hospital',
                'project_id' => $project->id,
                'latitude' => 21.4289,
                'longitude' => 39.8245,
                'description' => 'مستشفى عام متخصص',
                'is_active' => true,
                'min_zoom' => 14,
                'max_zoom' => 22,
                'display_order' => 3,
            ],
            [
                'name' => 'مركز التسوق الكبير',
                'type' => 'shopping_center',
                'project_id' => $project->id,
                'latitude' => 21.4234,
                'longitude' => 39.8298,
                'description' => 'مركز تسوق يحتوي على محلات ومطاعم متنوعة',
                'is_active' => true,
                'min_zoom' => 14,
                'max_zoom' => 22,
                'display_order' => 4,
            ],
            [
                'name' => 'حديقة الأطفال',
                'type' => 'park',
                'project_id' => $project->id,
                'latitude' => 21.4256,
                'longitude' => 39.8312,
                'description' => 'حديقة عامة للأطفال والعائلات',
                'is_active' => true,
                'min_zoom' => 14,
                'max_zoom' => 22,
                'display_order' => 5,
            ],
        ];

        foreach ($landmarks as $landmarkData) {
            Landmark::create($landmarkData);
        }

        $this->command->info('Landmarks seeded successfully!');
    }
}
