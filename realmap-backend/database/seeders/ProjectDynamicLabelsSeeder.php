<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\RealEstateProject;

class ProjectDynamicLabelsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Update existing projects with dynamic labels
        $projects = RealEstateProject::all();

        foreach ($projects as $project) {
            $project->update([
                // Descriptions
                'description_ar' => $project->description_ar ?? 'أراضي سكنية واستثمارية في قلب الخرج',
                'description_en' => $project->description_en ?? 'Residential and investment land in the heart of Al-Kharj',

                // Units labels
                'units_label_ar' => 'وحدة',
                'units_label_en'     => 'units',

                // Availability labels
                'availability_label_ar' => 'المتاح',
                'availability_label_en' => 'Available',

                // Price prefix
                'price_prefix_ar' => 'يبدأ من',
                'price_prefix_en' => 'Starting from',

                // Price labels
                'price_label_ar' => 'الأسعار',
                'price_label_en' => 'Prices',

                // Facilities
                'facilities_ar' => 'مساجد - مدارس - مواقف - حدائق',
                'facilities_en' => 'Mosques - Schools - Parking - Gardens',

                // Facilities labels
                'facilities_label_ar' => 'المرافق',
                'facilities_label_en' => 'Facilities',

                // Owner
                'owner_ar' => 'منصة أصل العقارية',
                'owner_en' => 'ASSL Real Estate Platform',

                // Owner labels
                'owner_label_ar' => 'المالك',
                'owner_label_en' => 'Owner',

                // Additional fields
                'total_units' => $project->total_units ?? 1828,
                'min_price' => $project->min_price ?? 200000,
                'max_price' => $project->max_price ?? 500000,
                'virtual_tour_url' => $project->virtual_tour_url ?? null,
                'share_url' => $project->share_url ?? null,
            ]);
        }

        // Create sample project with custom labels
        RealEstateProject::create([
            'name' => 'مخطط الواحة الذهبية',
            'description' => 'مشروع سكني متميز',
            'description_ar' => 'مشروع سكني متميز في قلب الرياض مع جميع الخدمات',
            'description_en' => 'Premium residential project in the heart of Riyadh with all services',

            'units_label_ar' => 'قطعة',
            'units_label_en' => 'plot',

            'availability_label_ar' => 'متوفر',
            'availability_label_en' => 'In Stock',

            'price_prefix_ar' => 'تبدأ الأسعار من',
            'price_prefix_en' => 'Prices start from',

            'price_label_ar' => 'التكلفة',
            'price_label_en' => 'Cost',

            'facilities_ar' => 'مسجد - مدرسة دولية - نادي صحي - حديقة مركزية',
            'facilities_en' => 'Mosque - International School - Health Club - Central Park',

            'facilities_label_ar' => 'الخدمات',
            'facilities_label_en' => 'Services',

            'owner_ar' => 'شركة الواحة للتطوير العقاري',
            'owner_en' => 'Al-Waaha Real Estate Development Company',

            'owner_label_ar' => 'المطور',
            'owner_label_en' => 'Developer',

            'total_units' => 2500,
            'min_price' => 150000,
            'max_price' => 800000,
            'virtual_tour_url' => 'https://example.com/360-tour-golden-oasis',
            'share_url' => 'https://example.com/projects/golden-oasis',

            'latitude' => 24.7136,
            'longitude' => 46.6753,
            'city' => 'الرياض',
            'status' => 'active',
        ]);
    }
}
