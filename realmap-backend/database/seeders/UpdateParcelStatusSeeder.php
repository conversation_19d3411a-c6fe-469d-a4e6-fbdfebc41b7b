<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Parcel;
use Illuminate\Support\Facades\DB;

class UpdateParcelStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all parcels
        $parcels = Parcel::all();
        $totalParcels = $parcels->count();
        
        if ($totalParcels === 0) {
            $this->command->info('No parcels found to update.');
            return;
        }
        
        // Define status distribution (percentages)
        $statusDistribution = [
            'Available' => 60,    // 60% available
            'Reserved' => 25,     // 25% reserved  
            'Sold' => 15,         // 15% sold
        ];
        
        // Calculate counts for each status
        $availableCount = (int) ($totalParcels * $statusDistribution['Available'] / 100);
        $reservedCount = (int) ($totalParcels * $statusDistribution['Reserved'] / 100);
        $soldCount = $totalParcels - $availableCount - $reservedCount; // Remaining parcels
        
        $this->command->info("Updating parcel statuses:");
        $this->command->info("- Available: {$availableCount} parcels");
        $this->command->info("- Reserved: {$reservedCount} parcels");
        $this->command->info("- Sold: {$soldCount} parcels");
        $this->command->info("- Total: {$totalParcels} parcels");
        
        // Shuffle parcels to randomize selection
        $shuffledParcels = $parcels->shuffle();
        
        $updated = 0;
        
        // Update to Reserved status
        for ($i = 0; $i < $reservedCount; $i++) {
            $parcel = $shuffledParcels[$updated];
            $parcel->update(['sales_status' => 'Reserved']);
            $updated++;
        }
        
        // Update to Sold status
        for ($i = 0; $i < $soldCount; $i++) {
            $parcel = $shuffledParcels[$updated];
            $parcel->update(['sales_status' => 'Sold']);
            $updated++;
        }
        
        // Remaining parcels stay as Available (they should already be Available)
        
        // Verify the distribution
        $finalDistribution = Parcel::select('sales_status', DB::raw('count(*) as count'))
            ->groupBy('sales_status')
            ->get();
            
        $this->command->info("\nFinal distribution:");
        foreach ($finalDistribution as $status) {
            $percentage = round(($status->count / $totalParcels) * 100, 1);
            $this->command->info("- {$status->sales_status}: {$status->count} parcels ({$percentage}%)");
        }
        
        $this->command->info("\nParcel statuses updated successfully!");
    }
}
