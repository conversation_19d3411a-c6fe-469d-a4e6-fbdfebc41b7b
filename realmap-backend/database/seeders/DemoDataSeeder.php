<?php

namespace Database\Seeders;

use App\Models\Block;
use App\Models\MapLayer;
use App\Models\Parcel;
use App\Models\ParcelImage;
use App\Models\RealEstateProject;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
        ]);

        // Create regular user
        $user = User::create([
            'name' => 'Regular User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
        ]);

        // Create a real estate project
        $project = RealEstateProject::create([
            'name' => 'Al Madinah Hills',
            'description' => 'A luxury residential project in Madinah',
            'location' => 'Madinah, Saudi Arabia',
            'developer_name' => 'Saudi Developers',
            'developer_logo' => 'developer-logos/sample-logo.svg',
            'city' => 'Madinah',
            'geo_area' => 'Northern Area',
            'user_id' => $admin->id,
        ]);

        // Create map layers
        $mapLayer = MapLayer::create([
            'name' => 'background',
            'type' => 'image',
            'project_id' => $project->id,
            'file_path' => 'map-layers/background.png',
            'coordinates' => json_encode([
                [39.97449, 21.47098],
                [39.9824, 21.47076],
                [39.98206, 21.46015],
                [39.97414, 21.46037],
            ]),
            'opacity' => 1.0,
            'is_active' => true,
            'display_order' => 0,
        ]);

        // Create blocks
        $block1 = Block::create([
            'block_number' => 'A1',
            'project_id' => $project->id,
            'coordinates' => json_encode([
                'type' => 'Polygon',
                'coordinates' => [
                    [
                        [39.97832, 21.46557],
                        [39.97932, 21.46547],
                        [39.97922, 21.46447],
                        [39.97822, 21.46457],
                        [39.97832, 21.46557],
                    ]
                ]
            ]),
            'shape_length' => 400.0,
            'shape_area' => 10000.0,
            'centroid_x' => 39.97877,
            'centroid_y' => 21.46502,
        ]);

        $block2 = Block::create([
            'block_number' => 'A2',
            'project_id' => $project->id,
            'coordinates' => json_encode([
                'type' => 'Polygon',
                'coordinates' => [
                    [
                        [39.97932, 21.46547],
                        [39.98032, 21.46537],
                        [39.98022, 21.46437],
                        [39.97922, 21.46447],
                        [39.97932, 21.46547],
                    ]
                ]
            ]),
            'shape_length' => 400.0,
            'shape_area' => 10000.0,
            'centroid_x' => 39.97977,
            'centroid_y' => 21.46492,
        ]);

        // Create parcels for block 1
        $parcel1 = Parcel::create([
            'parcel_number' => 'A1-01',
            'block_id' => $block1->id,
            'coordinates' => json_encode([
                'type' => 'Polygon',
                'coordinates' => [
                    [
                        [39.97832, 21.46557],
                        [39.97882, 21.46552],
                        [39.97877, 21.46502],
                        [39.97827, 21.46507],
                        [39.97832, 21.46557],
                    ]
                ]
            ]),
            'plot_no' => '101',
            'property_type' => 'Residential',
            'usage_type' => 'Villa',
            'sales_status' => 'Available',
            'plot_condition' => 'Ready',
            'view' => 'Mountain',
            'area' => 250.0,
            'price_per_sqm' => 2000.0,
            'price_with_tax' => 525000.0,
            'expected_roi' => '15%',
            'delivery_date' => '2025-12-31',
            'down_payment' => '20%',
            'payment_terms' => '4 years installment',
            'infrastructure_status' => 'Completed',
            'services_available' => 'Water, Electricity, Internet',
        ]);

        $parcel2 = Parcel::create([
            'parcel_number' => 'A1-02',
            'block_id' => $block1->id,
            'coordinates' => json_encode([
                'type' => 'Polygon',
                'coordinates' => [
                    [
                        [39.97882, 21.46552],
                        [39.97932, 21.46547],
                        [39.97927, 21.46497],
                        [39.97877, 21.46502],
                        [39.97882, 21.46552],
                    ]
                ]
            ]),
            'plot_no' => '102',
            'property_type' => 'Residential',
            'usage_type' => 'Villa',
            'sales_status' => 'Reserved',
            'plot_condition' => 'Ready',
            'view' => 'Garden',
            'area' => 250.0,
            'price_per_sqm' => 2200.0,
            'price_with_tax' => 577500.0,
            'expected_roi' => '14%',
            'delivery_date' => '2025-12-31',
            'down_payment' => '20%',
            'payment_terms' => '4 years installment',
            'infrastructure_status' => 'Completed',
            'services_available' => 'Water, Electricity, Internet',
        ]);

        $parcel3 = Parcel::create([
            'parcel_number' => 'A1-03',
            'block_id' => $block1->id,
            'coordinates' => json_encode([
                'type' => 'Polygon',
                'coordinates' => [
                    [
                        [39.97827, 21.46507],
                        [39.97877, 21.46502],
                        [39.97872, 21.46452],
                        [39.97822, 21.46457],
                        [39.97827, 21.46507],
                    ]
                ]
            ]),
            'plot_no' => '103',
            'property_type' => 'Residential',
            'usage_type' => 'Villa',
            'sales_status' => 'Sold',
            'plot_condition' => 'Ready',
            'view' => 'Street',
            'area' => 250.0,
            'price_per_sqm' => 1900.0,
            'price_with_tax' => 498750.0,
            'expected_roi' => '13%',
            'delivery_date' => '2025-12-31',
            'down_payment' => '20%',
            'payment_terms' => '4 years installment',
            'infrastructure_status' => 'Completed',
            'services_available' => 'Water, Electricity, Internet',
        ]);

        $parcel4 = Parcel::create([
            'parcel_number' => 'A1-04',
            'block_id' => $block1->id,
            'coordinates' => json_encode([
                'type' => 'Polygon',
                'coordinates' => [
                    [
                        [39.97877, 21.46502],
                        [39.97927, 21.46497],
                        [39.97922, 21.46447],
                        [39.97872, 21.46452],
                        [39.97877, 21.46502],
                    ]
                ]
            ]),
            'plot_no' => '104',
            'property_type' => 'Residential',
            'usage_type' => 'Villa',
            'sales_status' => 'Available',
            'plot_condition' => 'Ready',
            'view' => 'Park',
            'area' => 250.0,
            'price_per_sqm' => 2100.0,
            'price_with_tax' => 551250.0,
            'expected_roi' => '15%',
            'delivery_date' => '2025-12-31',
            'down_payment' => '20%',
            'payment_terms' => '4 years installment',
            'infrastructure_status' => 'Completed',
            'services_available' => 'Water, Electricity, Internet',
        ]);

        // Add sample images for parcels
        $this->addSampleImages($parcel1, $parcel2, $parcel3, $parcel4);
    }

    private function addSampleImages($parcel1, $parcel2, $parcel3, $parcel4)
    {
        // Sample image URLs (using placeholder services)
        $sampleImages = [
            'https://picsum.photos/800/600?random=1',
            'https://picsum.photos/800/600?random=2',
            'https://picsum.photos/800/600?random=3',
            'https://picsum.photos/800/600?random=4',
            'https://picsum.photos/800/600?random=5',
            'https://picsum.photos/800/600?random=6',
            'https://picsum.photos/800/600?random=7',
            'https://picsum.photos/800/600?random=8',
        ];

        // Add images for parcel 1
        ParcelImage::create([
            'parcel_id' => $parcel1->id,
            'image_path' => $sampleImages[0],
            'original_name' => 'parcel_1_main.jpg',
            'alt_text' => 'Main view of parcel A1-01',
            'sort_order' => 1,
            'is_primary' => true,
            'file_size' => '2.5MB',
            'mime_type' => 'image/jpeg',
        ]);

        ParcelImage::create([
            'parcel_id' => $parcel1->id,
            'image_path' => $sampleImages[1],
            'original_name' => 'parcel_1_side.jpg',
            'alt_text' => 'Side view of parcel A1-01',
            'sort_order' => 2,
            'is_primary' => false,
            'file_size' => '2.1MB',
            'mime_type' => 'image/jpeg',
        ]);

        // Add images for parcel 2
        ParcelImage::create([
            'parcel_id' => $parcel2->id,
            'image_path' => $sampleImages[2],
            'original_name' => 'parcel_2_main.jpg',
            'alt_text' => 'Main view of parcel A1-02',
            'sort_order' => 1,
            'is_primary' => true,
            'file_size' => '2.8MB',
            'mime_type' => 'image/jpeg',
        ]);

        ParcelImage::create([
            'parcel_id' => $parcel2->id,
            'image_path' => $sampleImages[3],
            'original_name' => 'parcel_2_aerial.jpg',
            'alt_text' => 'Aerial view of parcel A1-02',
            'sort_order' => 2,
            'is_primary' => false,
            'file_size' => '3.1MB',
            'mime_type' => 'image/jpeg',
        ]);

        ParcelImage::create([
            'parcel_id' => $parcel2->id,
            'image_path' => $sampleImages[4],
            'original_name' => 'parcel_2_street.jpg',
            'alt_text' => 'Street view of parcel A1-02',
            'sort_order' => 3,
            'is_primary' => false,
            'file_size' => '2.3MB',
            'mime_type' => 'image/jpeg',
        ]);

        // Add images for parcel 3
        ParcelImage::create([
            'parcel_id' => $parcel3->id,
            'image_path' => $sampleImages[5],
            'original_name' => 'parcel_3_main.jpg',
            'alt_text' => 'Main view of parcel A1-03',
            'sort_order' => 1,
            'is_primary' => true,
            'file_size' => '2.7MB',
            'mime_type' => 'image/jpeg',
        ]);

        // Add images for parcel 4
        ParcelImage::create([
            'parcel_id' => $parcel4->id,
            'image_path' => $sampleImages[6],
            'original_name' => 'parcel_4_main.jpg',
            'alt_text' => 'Main view of parcel A1-04',
            'sort_order' => 1,
            'is_primary' => true,
            'file_size' => '2.4MB',
            'mime_type' => 'image/jpeg',
        ]);

        ParcelImage::create([
            'parcel_id' => $parcel4->id,
            'image_path' => $sampleImages[7],
            'original_name' => 'parcel_4_corner.jpg',
            'alt_text' => 'Corner view of parcel A1-04',
            'sort_order' => 2,
            'is_primary' => false,
            'file_size' => '2.9MB',
            'mime_type' => 'image/jpeg',
        ]);
    }
}
