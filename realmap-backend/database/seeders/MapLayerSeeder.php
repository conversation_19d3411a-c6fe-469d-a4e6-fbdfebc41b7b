<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\MapLayer;
use App\Models\RealEstateProject;

class MapLayerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first project (Al Madinah Hills)
        $project = RealEstateProject::first();

        if (!$project) {
            $this->command->info('No projects found. Please run DemoDataSeeder first.');
            return;
        }

        // Create sample map layers
        MapLayer::create([
            'name' => 'satellite_background',
            'type' => 'image',
            'project_id' => $project->id,
            'file_path' => 'map-layers/sample-satellite.svg',
            'coordinates' => json_encode([
                [39.970, 21.460],
                [39.985, 21.460],
                [39.985, 21.470],
                [39.970, 21.470]
            ]),
            'opacity' => 0.8,
            'min_zoom' => 10,
            'max_zoom' => 18,
            'is_active' => true,
            'display_order' => 1,
        ]);

        MapLayer::create([
            'name' => 'project_overlay',
            'type' => 'image',
            'project_id' => $project->id,
            'file_path' => 'map-layers/sample-overlay.svg',
            'coordinates' => json_encode([
                [39.975, 21.463],
                [39.980, 21.463],
                [39.980, 21.467],
                [39.975, 21.467]
            ]),
            'opacity' => 0.6,
            'min_zoom' => 12,
            'max_zoom' => 20,
            'is_active' => true,
            'display_order' => 2,
        ]);

        $this->command->info('Map layers seeded successfully!');
    }
}
