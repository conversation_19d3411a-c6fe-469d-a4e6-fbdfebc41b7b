<?php

namespace Database\Seeders;

use App\Models\Block;
use App\Models\MapLayer;
use App\Models\Parcel;
use App\Models\RealEstateProject;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class TestProject101Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get admin user
        $admin = User::where('email', '<EMAIL>')->first();
        if (!$admin) {
            $admin = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'role' => 'admin',
            ]);
        }

        // Create Test Project 101
        $project = RealEstateProject::create([
            'name' => 'Test Project 101',
            'description' => 'Test project imported from oldWEBapp with complete data',
            'location' => 'Al Madinah, Saudi Arabia',
            'developer_name' => 'Al Ekaz Developments',
            'city' => 'Al Madinah',
            'geo_area' => 'Al Ekaz Area',
            'user_id' => $admin->id,
        ]);

        // Copy background image to storage
        $oldImagePath = base_path('../oldWEBapp/public/003.png');
        $newImagePath = 'map-layers/test-project-101-background.png';

        if (File::exists($oldImagePath)) {
            Storage::disk('public')->put($newImagePath, File::get($oldImagePath));
        }

        // Create map layer for background image
        MapLayer::create([
            'name' => 'background',
            'type' => 'image',
            'project_id' => $project->id,
            'file_path' => $newImagePath,
            'coordinates' => json_encode([
                [39.97449, 21.47098],
                [39.9824, 21.47076],
                [39.98206, 21.46015],
                [39.97414, 21.46037],
            ]),
            'opacity' => 0.6,
            'is_active' => true,
            'display_order' => 0,
        ]);

        // Read and process Blocks.geojson
        $this->importBlocks($project);

        // Read and process Parcels.geojson
        $this->importParcels($project);

        $this->command->info('Test Project 101 imported successfully!');
    }

    private function importBlocks($project)
    {
        $blocksJsonPath = base_path('../oldWEBapp/public/Blocks.geojson');

        if (!File::exists($blocksJsonPath)) {
            $this->command->error('Blocks.geojson file not found');
            return;
        }

        $blocksData = json_decode(File::get($blocksJsonPath), true);

        foreach ($blocksData['features'] as $feature) {
            $properties = $feature['properties'];
            $geometry = $feature['geometry'];

            Block::create([
                'block_number' => $properties['BlockNo'],
                'project_id' => $project->id,
                'coordinates' => json_encode($geometry),
                'shape_length' => $properties['Shape_Length'] ?? 0,
                'shape_area' => $properties['Shape_Area'] ?? 0,
                'centroid_x' => $properties['Centroid_X'] ?? 0,
                'centroid_y' => $properties['Centroid_Y'] ?? 0,
            ]);
        }

        $this->command->info('Imported ' . count($blocksData['features']) . ' blocks');
    }

    private function importParcels($project)
    {
        $parcelsJsonPath = base_path('../oldWEBapp/public/Parcels.geojson');

        if (!File::exists($parcelsJsonPath)) {
            $this->command->error('Parcels.geojson file not found');
            return;
        }

        $parcelsData = json_decode(File::get($parcelsJsonPath), true);

        foreach ($parcelsData['features'] as $feature) {
            $properties = $feature['properties'];
            $geometry = $feature['geometry'];

            // Find the corresponding block
            $block = Block::where('project_id', $project->id)
                ->where('block_number', $properties['Block_No'])
                ->first();

            if (!$block) {
                $this->command->warn('Block not found for parcel: ' . $properties['Parcel_No']);
                continue;
            }

            // Extract area from total_area string (e.g., "984 sqm" -> 984)
            $area = 0;
            if (isset($properties['total_area'])) {
                preg_match('/(\d+)/', $properties['total_area'], $matches);
                $area = isset($matches[1]) ? (float)$matches[1] : 0;
            }

            // Extract price from price_with_tax string (e.g., "495936 AED" -> 495936)
            $price = 0;
            if (isset($properties['price_with_tax'])) {
                preg_match('/(\d+)/', $properties['price_with_tax'], $matches);
                $price = isset($matches[1]) ? (float)$matches[1] : 0;
            }

            // Extract price per sqm
            $pricePerSqm = 0;
            if (isset($properties['price_per_sqm'])) {
                preg_match('/(\d+)/', $properties['price_per_sqm'], $matches);
                $pricePerSqm = isset($matches[1]) ? (float)$matches[1] : 0;
            }

            Parcel::create([
                'parcel_number' => $properties['Parcel_No'],
                'block_id' => $block->id,
                'coordinates' => json_encode($geometry),
                'plot_no' => $properties['Plot_No'] ?? '',
                'property_type' => $properties['property_type'] ?? 'Land',
                'usage_type' => $properties['usage_type'] ?? 'Residential',
                'sales_status' => $properties['sales_status'] ?? 'Available',
                'plot_condition' => $properties['plot_condition'] ?? 'Raw',
                'view' => $properties['view'] ?? '',
                'area' => $area,
                'price_per_sqm' => $pricePerSqm,
                'price_with_tax' => $price,
                'expected_roi' => $properties['expected_roi'] ?? '',
                'delivery_date' => $properties['delivery_date'] ?? null,
                'down_payment' => $properties['down_payment'] ?? '',
                'payment_terms' => $properties['payment_terms'] ?? '',
                'infrastructure_status' => $properties['infrastructure_status'] ?? '',
                'services_available' => $properties['services_available'] ?? '',
            ]);
        }

        $this->command->info('Imported ' . count($parcelsData['features']) . ' parcels');
    }
}
