<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('real_estate_projects', function (Blueprint $table) {
            $table->string('blocks_geojson_file')->nullable()->after('user_id');
            $table->string('parcels_geojson_file')->nullable()->after('blocks_geojson_file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('real_estate_projects', function (Blueprint $table) {
            $table->dropColumn(['blocks_geojson_file', 'parcels_geojson_file']);
        });
    }
};
