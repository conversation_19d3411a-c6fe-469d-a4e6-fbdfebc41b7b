<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('landmarks', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // mosque, school, hospital, etc.
            $table->foreignId('project_id')->constrained('real_estate_projects')->onDelete('cascade');
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->string('icon_path')->nullable(); // Path to custom icon/logo
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('min_zoom')->default(14); // Show at zoom level 14+
            $table->integer('max_zoom')->default(22);
            $table->integer('display_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('landmarks');
    }
};
