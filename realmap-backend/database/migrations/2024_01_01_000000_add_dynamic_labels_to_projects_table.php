<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('real_estate_projects', function (Blueprint $table) {
            // Description fields for multilingual support
            $table->text('description_ar')->nullable()->after('description');
            $table->text('description_en')->nullable()->after('description_ar');
            
            // Units label fields
            $table->string('units_label_ar')->nullable()->default('وحدة');
            $table->string('units_label_en')->nullable()->default('units');
            
            // Availability label fields
            $table->string('availability_label_ar')->nullable()->default('المتاح');
            $table->string('availability_label_en')->nullable()->default('Available');
            
            // Price prefix fields
            $table->string('price_prefix_ar')->nullable()->default('يبدأ من');
            $table->string('price_prefix_en')->nullable()->default('Starting from');
            
            // Price label fields
            $table->string('price_label_ar')->nullable()->default('الأسعار');
            $table->string('price_label_en')->nullable()->default('Prices');
            
            // Facilities fields
            $table->text('facilities_ar')->nullable()->default('مساجد - مدارس - مواقف - حدائق');
            $table->text('facilities_en')->nullable()->default('Mosques - Schools - Parking - Gardens');
            
            // Facilities label fields
            $table->string('facilities_label_ar')->nullable()->default('المرافق');
            $table->string('facilities_label_en')->nullable()->default('Facilities');
            
            // Owner fields
            $table->string('owner_ar')->nullable()->default('منصة أصل العقارية');
            $table->string('owner_en')->nullable()->default('ASSL Real Estate Platform');
            
            // Owner label fields
            $table->string('owner_label_ar')->nullable()->default('المالك');
            $table->string('owner_label_en')->nullable()->default('Owner');
            
            // Additional dynamic fields
            $table->integer('total_units')->nullable();
            $table->decimal('min_price', 15, 2)->nullable();
            $table->decimal('max_price', 15, 2)->nullable();

            // 360 Virtual Tour URL
            $table->string('virtual_tour_url')->nullable();

            // Custom Share URL
            $table->string('share_url')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('real_estate_projects', function (Blueprint $table) {
            $table->dropColumn([
                'description_ar',
                'description_en',
                'units_label_ar',
                'units_label_en',
                'availability_label_ar',
                'availability_label_en',
                'price_prefix_ar',
                'price_prefix_en',
                'price_label_ar',
                'price_label_en',
                'facilities_ar',
                'facilities_en',
                'facilities_label_ar',
                'facilities_label_en',
                'owner_ar',
                'owner_en',
                'owner_label_ar',
                'owner_label_en',
                'total_units',
                'min_price',
                'max_price',
                'virtual_tour_url',
                'share_url'
            ]);
        });
    }
};
