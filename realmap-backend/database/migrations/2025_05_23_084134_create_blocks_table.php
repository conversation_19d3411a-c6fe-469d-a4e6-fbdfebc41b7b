<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blocks', function (Blueprint $table) {
            $table->id();
            $table->string('block_number');
            $table->foreignId('project_id')->constrained('real_estate_projects')->onDelete('cascade');
            $table->json('coordinates')->nullable(); // GeoJSON coordinates
            $table->decimal('shape_length', 15, 8)->nullable();
            $table->decimal('shape_area', 15, 8)->nullable();
            $table->decimal('centroid_x', 15, 8)->nullable();
            $table->decimal('centroid_y', 15, 8)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blocks');
    }
};
