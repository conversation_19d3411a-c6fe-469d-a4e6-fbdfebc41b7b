<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('map_layers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // image, geojson
            $table->foreignId('project_id')->constrained('real_estate_projects')->onDelete('cascade');
            $table->string('file_path');
            $table->json('coordinates')->nullable(); // For image layers
            $table->decimal('opacity', 3, 2)->default(1.00);
            $table->integer('min_zoom')->nullable();
            $table->integer('max_zoom')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('display_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('map_layers');
    }
};
