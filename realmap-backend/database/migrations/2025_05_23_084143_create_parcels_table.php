<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('parcels', function (Blueprint $table) {
            $table->id();
            $table->string('parcel_number');
            $table->foreignId('block_id')->constrained()->onDelete('cascade');
            $table->json('coordinates')->nullable(); // GeoJSON coordinates
            $table->string('plot_no')->nullable();
            $table->string('property_type')->nullable();
            $table->string('usage_type')->nullable();
            $table->string('sales_status')->default('Available'); // Available, Reserved, Sold
            $table->string('plot_condition')->nullable();
            $table->string('view')->nullable();
            $table->decimal('area', 15, 2)->nullable();
            $table->decimal('price_per_sqm', 15, 2)->nullable();
            $table->decimal('price_with_tax', 15, 2)->nullable();
            $table->string('expected_roi')->nullable();
            $table->date('delivery_date')->nullable();
            $table->string('down_payment')->nullable();
            $table->string('payment_terms')->nullable();
            $table->string('infrastructure_status')->nullable();
            $table->string('services_available')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('parcels');
    }
};
