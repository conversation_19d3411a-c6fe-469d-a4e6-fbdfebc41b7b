<?php

use App\Http\Controllers\Api\BlockController;
use App\Http\Controllers\Api\LandmarkController;
use App\Http\Controllers\Api\MapLayerController;
use App\Http\Controllers\Api\ParcelController;
use App\Http\Controllers\Api\RealEstateProjectController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Authentication routes
Route::middleware('auth:sanctum')->group(function () {
    Route::get('/user', function (Request $request) {
        return $request->user();
    });
});

// Cached API routes (1 week cache duration)//10080
// Route::middleware('cache.response:10080')->group(function () {

// Real Estate Projects
Route::prefix('projects')->group(function () {
    Route::get('/', [RealEstateProjectController::class, 'index']);
    Route::get('/{id}', [RealEstateProjectController::class, 'show']);
    Route::get('/{projectId}/blocks/geojson', [BlockController::class, 'getGeoJson']);
    Route::get('/{projectId}/map-layers', [MapLayerController::class, 'getProjectLayers']);
});

// Blocks
Route::prefix('blocks')->group(function () {
    Route::get('/', [BlockController::class, 'index']);
    Route::get('/{id}', [BlockController::class, 'show']);
    Route::get('/{blockId}/parcels/geojson', [ParcelController::class, 'getGeoJson']);
});

// Parcels
Route::prefix('parcels')->group(function () {
    Route::get('/', [ParcelController::class, 'index']);
    Route::get('/search', [ParcelController::class, 'search']);
    Route::get('/{id}', [ParcelController::class, 'show']);
});

// Map Layers
Route::prefix('map-layers')->group(function () {
    Route::get('/', [MapLayerController::class, 'index']);
    Route::get('/{id}', [MapLayerController::class, 'show']);
    Route::get('/file/{filename}', [MapLayerController::class, 'serveFile']);
});

// Landmarks
Route::prefix('landmarks')->group(function () {
    Route::get('/', [LandmarkController::class, 'index']);
    Route::get('/{id}', [LandmarkController::class, 'show']);
});

// Project Landmarks
Route::get('/projects/{projectId}/landmarks', [LandmarkController::class, 'getProjectLandmarks']);

// });
