# GeoJSON File Upload Feature

## Overview
تم تحديث لوحة التحكم لتدعم رفع ملفات GeoJSON عند إنشاء مشروع جديد. يمكن الآن رفع ملفات:
- `Blocks.geojson` - لبيانات البلوكات
- `Parcels.geojson` - لبيانات القطع

## Features Added

### 1. Database Changes
- إضافة حقلين جديدين لجدول `real_estate_projects`:
  - `blocks_geojson_path` - مسار ملف البلوكات
  - `parcels_geojson_path` - مسار ملف القطع

### 2. File Upload Support
- رفع ملفات JSON/GeoJSON بحد أقصى 10MB
- تخزين الملفات في `storage/app/public/geojson-uploads/`
- التحقق من صحة تنسيق GeoJSON

### 3. Automatic Import
- استيراد تلقائي للبيانات بعد رفع الملفات
- إنشاء البلوكات والقطع في قاعدة البيانات
- إشعارات للمستخدم بحالة الاستيراد

### 4. Enhanced UI
- واجهة محسنة مع أقسام منفصلة
- أيقونات لإظهار حالة الملفات المرفوعة
- عداد للبلوكات المستوردة

## How to Use

### 1. Create New Project
1. اذهب إلى لوحة التحكم
2. انقر على "Projects" → "Create"
3. املأ معلومات المشروع الأساسية
4. في قسم "GeoJSON Files":
   - ارفع ملف `Blocks.geojson`
   - ارفع ملف `Parcels.geojson`
5. انقر "Create"

### 2. File Format Requirements
ملفات GeoJSON يجب أن تحتوي على:

#### For Blocks:
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": { ... },
      "properties": {
        "BlockNo": "A1",
        "Shape_Length": 400.0,
        "Shape_Area": 10000.0,
        "Centroid_X": 39.97877,
        "Centroid_Y": 21.46502
      }
    }
  ]
}
```

#### For Parcels:
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": { ... },
      "properties": {
        "Parcel_No": "P001",
        "Block_No": "A1",
        "total_area": "984 sqm",
        "price_per_sqm": "1500 AED",
        "price_with_tax": "1476000 AED",
        "sales_status": "Available",
        "property_type": "Residential",
        "usage_type": "Villa"
      }
    }
  ]
}
```

## Technical Details

### Files Modified/Created:
1. `database/migrations/2025_05_26_082449_add_geojson_files_to_real_estate_projects_table.php`
2. `app/Models/RealEstateProject.php`
3. `app/Services/GeoJsonImportService.php` (new)
4. `app/Filament/Resources/RealEstateProjectResource.php`
5. `app/Filament/Resources/RealEstateProjectResource/Pages/CreateRealEstateProject.php`

### Key Features:
- **Validation**: التحقق من صحة تنسيق GeoJSON قبل الحفظ
- **Error Handling**: معالجة الأخطاء مع إشعارات واضحة
- **Flexible Mapping**: دعم أسماء خصائص متعددة للحقول
- **Automatic Relationships**: ربط تلقائي بين القطع والبلوكات

### Storage Structure:
```
storage/app/public/
├── geojson-uploads/
│   ├── blocks_[timestamp]_[filename].geojson
│   └── parcels_[timestamp]_[filename].geojson
└── map-layers/
    └── [existing map files]
```

## Testing
لاختبار النظام:
1. استخدم الملفات الموجودة في `oldWEBapp/public/`
2. ارفع `Blocks.geojson` و `Parcels.geojson`
3. تحقق من الإشعارات بعد الحفظ
4. تأكد من إنشاء البيانات في قاعدة البيانات

## Error Handling
النظام يتعامل مع:
- ملفات JSON غير صحيحة
- ملفات GeoJSON بتنسيق خاطئ
- قطع بدون بلوكات مطابقة
- أخطاء قاعدة البيانات
- ملفات كبيرة الحجم

## Future Enhancements
- دعم تحديث الملفات للمشاريع الموجودة
- معاينة البيانات قبل الاستيراد
- تصدير البيانات إلى GeoJSON
- دعم ملفات إضافية (KML, Shapefile)
