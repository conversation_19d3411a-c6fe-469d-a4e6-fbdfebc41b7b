<?php

namespace App\Filament\Resources;

use App\Filament\Resources\LandmarkResource\Pages;
use App\Filament\Resources\LandmarkResource\RelationManagers;
use App\Models\Landmark;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LandmarkResource extends Resource
{
    protected static ?string $model = Landmark::class;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    protected static ?string $navigationLabel = 'النقاط المعلمة';

    protected static ?string $modelLabel = 'نقطة معلمة';

    protected static ?string $pluralModelLabel = 'النقاط المعلمة';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('معلومات النقطة المعلمة')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('اسم النقطة')
                            ->required()
                            ->maxLength(255),

                        Forms\Components\Select::make('type')
                            ->label('نوع النقطة')
                            ->options(Landmark::getTypeOptions())
                            ->required(),

                        Forms\Components\Select::make('project_id')
                            ->label('المشروع')
                            ->relationship('project', 'name')
                            ->required(),

                        Forms\Components\Textarea::make('description')
                            ->label('الوصف')
                            ->columnSpanFull(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('الموقع الجغرافي')
                    ->schema([
                        Forms\Components\TextInput::make('latitude')
                            ->label('خط العرض')
                            ->required()
                            ->numeric()
                            ->step(0.00000001)
                            ->helperText('مثال: 21.4667'),

                        Forms\Components\TextInput::make('longitude')
                            ->label('خط الطول')
                            ->required()
                            ->numeric()
                            ->step(0.00000001)
                            ->helperText('مثال: 39.9778'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('الأيقونة والإعدادات')
                    ->schema([
                        Forms\Components\FileUpload::make('icon_path')
                            ->label('أيقونة النقطة')
                            ->image()
                            ->disk('public')
                            ->directory('landmark-icons')
                            ->visibility('public')
                            ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg', 'image/svg+xml'])
                            ->maxSize(1024) // 1MB
                            ->imageResizeMode('cover')
                            ->imageCropAspectRatio('1:1')
                            ->imageResizeTargetWidth('64')
                            ->imageResizeTargetHeight('64')
                            ->helperText('حجم مُوصى به: 64x64 بكسل'),

                        Forms\Components\TextInput::make('min_zoom')
                            ->label('أقل مستوى تكبير')
                            ->numeric()
                            ->default(14)
                            ->minValue(1)
                            ->maxValue(22)
                            ->helperText('مستوى التكبير الذي تظهر فيه النقطة (افتراضي: 14)'),

                        Forms\Components\TextInput::make('max_zoom')
                            ->label('أعلى مستوى تكبير')
                            ->numeric()
                            ->default(22)
                            ->minValue(1)
                            ->maxValue(22),

                        Forms\Components\TextInput::make('display_order')
                            ->label('ترتيب العرض')
                            ->numeric()
                            ->default(0)
                            ->helperText('ترتيب ظهور النقطة (الأرقام الأصغر تظهر أولاً)'),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('اسم النقطة')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('النوع')
                    ->formatStateUsing(fn (string $state): string => Landmark::getTypeOptions()[$state] ?? $state)
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'mosque' => 'success',
                        'school' => 'info',
                        'hospital' => 'danger',
                        'shopping_center' => 'warning',
                        'park' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('project.name')
                    ->label('المشروع')
                    ->sortable(),

                Tables\Columns\TextColumn::make('latitude')
                    ->label('خط العرض')
                    ->numeric(decimalPlaces: 6),

                Tables\Columns\TextColumn::make('longitude')
                    ->label('خط الطول')
                    ->numeric(decimalPlaces: 6),

                Tables\Columns\ImageColumn::make('icon_path')
                    ->label('الأيقونة')
                    ->disk('public')
                    ->size(40),

                Tables\Columns\TextColumn::make('min_zoom')
                    ->label('أقل تكبير')
                    ->numeric(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean(),

                Tables\Columns\TextColumn::make('display_order')
                    ->label('ترتيب العرض')
                    ->numeric()
                    ->sortable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('تاريخ الإنشاء')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('نوع النقطة')
                    ->options(Landmark::getTypeOptions()),

                Tables\Filters\SelectFilter::make('project')
                    ->label('المشروع')
                    ->relationship('project', 'name'),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('display_order');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListLandmarks::route('/'),
            'create' => Pages\CreateLandmark::route('/create'),
            'edit' => Pages\EditLandmark::route('/{record}/edit'),
        ];
    }
}
