<?php

namespace App\Filament\Resources\RealEstateProjectResource\Pages;

use App\Filament\Resources\RealEstateProjectResource;
use App\Services\GeoJsonImportService;
use Filament\Resources\Pages\CreateRecord;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class CreateRealEstateProject extends CreateRecord
{
    protected static string $resource = RealEstateProjectResource::class;

    protected function afterCreate(): void
    {
        $record = $this->record;
        $geoJsonService = new GeoJsonImportService();
        $importResults = [];

        Log::info('Starting GeoJSON import for project: ' . $record->name, [
            'project_id' => $record->id,
            'blocks_file' => $record->blocks_geojson_file,
            'parcels_file' => $record->parcels_geojson_file
        ]);

        // Import blocks if file was uploaded
        if ($record->blocks_geojson_file) {
            Log::info('Importing blocks from file: ' . $record->blocks_geojson_file);

            $blocksResult = $geoJsonService->importBlocks($record, $record->blocks_geojson_file);
            $importResults[] = $blocksResult;

            if ($blocksResult['success']) {
                Notification::make()
                    ->title('Blocks Imported Successfully')
                    ->body($blocksResult['message'])
                    ->success()
                    ->send();

                Log::info('Blocks import successful', ['imported_count' => $blocksResult['imported_count']]);
            } else {
                Notification::make()
                    ->title('Blocks Import Failed')
                    ->body($blocksResult['message'])
                    ->danger()
                    ->send();

                Log::error('Blocks import failed', ['errors' => $blocksResult['errors']]);
            }

            // Delete the temporary file
            if (Storage::disk('local')->exists($record->blocks_geojson_file)) {
                Storage::disk('local')->delete($record->blocks_geojson_file);
                Log::info('Deleted temporary blocks file: ' . $record->blocks_geojson_file);
            }
        }

        // Import parcels if file was uploaded
        if ($record->parcels_geojson_file) {
            Log::info('Importing parcels from file: ' . $record->parcels_geojson_file);

            $parcelsResult = $geoJsonService->importParcels($record, $record->parcels_geojson_file);
            $importResults[] = $parcelsResult;

            if ($parcelsResult['success']) {
                Notification::make()
                    ->title('Parcels Imported Successfully')
                    ->body($parcelsResult['message'])
                    ->success()
                    ->send();

                Log::info('Parcels import successful', ['imported_count' => $parcelsResult['imported_count']]);
            } else {
                Notification::make()
                    ->title('Parcels Import Failed')
                    ->body($parcelsResult['message'])
                    ->danger()
                    ->send();

                Log::error('Parcels import failed', ['errors' => $parcelsResult['errors']]);
            }

            // Delete the temporary file
            if (Storage::disk('local')->exists($record->parcels_geojson_file)) {
                Storage::disk('local')->delete($record->parcels_geojson_file);
                Log::info('Deleted temporary parcels file: ' . $record->parcels_geojson_file);
            }
        }

        // Show summary notification if both files were processed
        if (count($importResults) > 1) {
            $totalImported = array_sum(array_column($importResults, 'imported_count'));
            $totalErrors = array_sum(array_map(fn($result) => count($result['errors']), $importResults));

            Notification::make()
                ->title('Import Summary')
                ->body("Total imported: {$totalImported} items" . ($totalErrors > 0 ? " with {$totalErrors} errors" : ""))
                ->info()
                ->send();

            Log::info('Import summary', [
                'total_imported' => $totalImported,
                'total_errors' => $totalErrors
            ]);
        }

        // Clear the file paths from the record since we don't want to store them
        $record->update([
            'blocks_geojson_file' => null,
            'parcels_geojson_file' => null
        ]);
    }
}
