<?php

namespace App\Filament\Resources\RealEstateProjectResource\RelationManagers;

use App\Models\Landmark;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class LandmarksRelationManager extends RelationManager
{
    protected static string $relationship = 'landmarks';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('اسم النقطة')
                    ->required()
                    ->maxLength(255),

                Forms\Components\Select::make('type')
                    ->label('نوع النقطة')
                    ->options(Landmark::getTypeOptions())
                    ->required(),

                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make('latitude')
                            ->label('خط العرض')
                            ->required()
                            ->numeric()
                            ->step(0.00000001)
                            ->helperText('مثال: 21.4667'),

                        Forms\Components\TextInput::make('longitude')
                            ->label('خط الطول')
                            ->required()
                            ->numeric()
                            ->step(0.00000001)
                            ->helperText('مثال: 39.9778'),
                    ]),

                Forms\Components\FileUpload::make('icon_path')
                    ->label('أيقونة النقطة')
                    ->image()
                    ->disk('public')
                    ->directory('landmark-icons')
                    ->visibility('public')
                    ->acceptedFileTypes(['image/png', 'image/jpg', 'image/jpeg', 'image/svg+xml'])
                    ->maxSize(1024) // 1MB
                    ->imageResizeMode('cover')
                    ->imageCropAspectRatio('1:1')
                    ->imageResizeTargetWidth('64')
                    ->imageResizeTargetHeight('64'),

                Forms\Components\Textarea::make('description')
                    ->label('الوصف')
                    ->columnSpanFull(),

                Forms\Components\Grid::make(3)
                    ->schema([
                        Forms\Components\TextInput::make('min_zoom')
                            ->label('أقل مستوى تكبير')
                            ->numeric()
                            ->default(14)
                            ->minValue(1)
                            ->maxValue(22),

                        Forms\Components\TextInput::make('display_order')
                            ->label('ترتيب العرض')
                            ->numeric()
                            ->default(0),

                        Forms\Components\Toggle::make('is_active')
                            ->label('نشط')
                            ->default(true),
                    ]),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('اسم النقطة')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->label('النوع')
                    ->formatStateUsing(fn (string $state): string => Landmark::getTypeOptions()[$state] ?? $state)
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'mosque' => 'success',
                        'school' => 'info',
                        'hospital' => 'danger',
                        'shopping_center' => 'warning',
                        'park' => 'success',
                        default => 'gray',
                    }),

                Tables\Columns\TextColumn::make('latitude')
                    ->label('خط العرض')
                    ->numeric(decimalPlaces: 6),

                Tables\Columns\TextColumn::make('longitude')
                    ->label('خط الطول')
                    ->numeric(decimalPlaces: 6),

                Tables\Columns\ImageColumn::make('icon_path')
                    ->label('الأيقونة')
                    ->disk('public')
                    ->size(40),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('نشط')
                    ->boolean(),

                Tables\Columns\TextColumn::make('display_order')
                    ->label('ترتيب العرض')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->label('نوع النقطة')
                    ->options(Landmark::getTypeOptions()),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('نشط'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('إضافة نقطة معلمة'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('display_order');
    }
}
