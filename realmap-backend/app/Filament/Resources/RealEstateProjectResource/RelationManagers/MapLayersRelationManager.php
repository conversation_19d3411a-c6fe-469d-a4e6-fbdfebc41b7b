<?php

namespace App\Filament\Resources\RealEstateProjectResource\RelationManagers;

use App\Models\MapLayer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class MapLayersRelationManager extends RelationManager
{
    protected static string $relationship = 'mapLayers';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('type')
                    ->options([
                        'image' => 'Image',
                        'geojson' => 'GeoJSON',
                    ])
                    ->required(),
                Forms\Components\FileUpload::make('file_path')
                    ->required()
                    ->disk('public')
                    ->directory('map-layers'),
                Forms\Components\Textarea::make('coordinates')
                    ->label('GeoJSON Coordinates')
                    ->helperText('For image layers, enter the coordinates in the format [[lng1, lat1], [lng2, lat2], [lng3, lat3], [lng4, lat4]]')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('opacity')
                    ->numeric()
                    ->default(1.00)
                    ->step(0.01)
                    ->minValue(0)
                    ->maxValue(1),
                Forms\Components\TextInput::make('min_zoom')
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(22),
                Forms\Components\TextInput::make('max_zoom')
                    ->numeric()
                    ->minValue(0)
                    ->maxValue(22),
                Forms\Components\Toggle::make('is_active')
                    ->default(true),
                Forms\Components\TextInput::make('display_order')
                    ->numeric()
                    ->default(0),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'image' => 'success',
                        'geojson' => 'info',
                        default => 'gray',
                    }),
                Tables\Columns\IconColumn::make('is_active')
                    ->boolean(),
                Tables\Columns\TextColumn::make('display_order')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
