<?php

namespace App\Filament\Resources\RealEstateProjectResource\RelationManagers;

use App\Models\Block;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class BlocksRelationManager extends RelationManager
{
    protected static string $relationship = 'blocks';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('block_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('coordinates')
                    ->label('GeoJSON Coordinates')
                    ->helperText('Enter valid GeoJSON coordinates for the block')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('shape_length')
                    ->numeric()
                    ->label('Shape Length'),
                Forms\Components\TextInput::make('shape_area')
                    ->numeric()
                    ->label('Shape Area'),
                Forms\Components\TextInput::make('centroid_x')
                    ->numeric()
                    ->label('Centroid X'),
                Forms\Components\TextInput::make('centroid_y')
                    ->numeric()
                    ->label('Centroid Y'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('block_number')
            ->columns([
                Tables\Columns\TextColumn::make('block_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('shape_area')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('parcels_count')
                    ->counts('parcels')
                    ->label('Parcels'),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
                Tables\Actions\AttachAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
