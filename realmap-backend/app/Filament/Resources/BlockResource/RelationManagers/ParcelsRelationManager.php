<?php

namespace App\Filament\Resources\BlockResource\RelationManagers;

use App\Models\Parcel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ParcelsRelationManager extends RelationManager
{
    protected static string $relationship = 'parcels';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('parcel_number')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('coordinates')
                    ->label('GeoJSON Coordinates')
                    ->helperText('Enter valid GeoJSON coordinates for the parcel')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('plot_no')
                    ->maxLength(255),
                Forms\Components\TextInput::make('property_type')
                    ->maxLength(255),
                Forms\Components\TextInput::make('usage_type')
                    ->maxLength(255),
                Forms\Components\Select::make('sales_status')
                    ->options([
                        'Available' => 'Available',
                        'Reserved' => 'Reserved',
                        'Sold' => 'Sold',
                    ])
                    ->default('Available')
                    ->required(),
                Forms\Components\TextInput::make('plot_condition')
                    ->maxLength(255),
                Forms\Components\TextInput::make('view')
                    ->maxLength(255),
                Forms\Components\TextInput::make('area')
                    ->numeric()
                    ->label('Area (sqm)'),
                Forms\Components\TextInput::make('price_per_sqm')
                    ->numeric()
                    ->label('Price per sqm'),
                Forms\Components\TextInput::make('price_with_tax')
                    ->numeric()
                    ->label('Total Price (with tax)'),
                Forms\Components\TextInput::make('expected_roi')
                    ->maxLength(255),
                Forms\Components\DatePicker::make('delivery_date'),
                Forms\Components\TextInput::make('down_payment')
                    ->maxLength(255),
                Forms\Components\TextInput::make('payment_terms')
                    ->maxLength(255),
                Forms\Components\TextInput::make('infrastructure_status')
                    ->maxLength(255),
                Forms\Components\TextInput::make('services_available')
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('parcel_number')
            ->columns([
                Tables\Columns\TextColumn::make('parcel_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('area')
                    ->numeric()
                    ->sortable()
                    ->label('Area (sqm)'),
                Tables\Columns\TextColumn::make('price_with_tax')
                    ->money('AED')
                    ->sortable()
                    ->label('Price'),
                Tables\Columns\TextColumn::make('sales_status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Available' => 'success',
                        'Reserved' => 'warning',
                        'Sold' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('sales_status')
                    ->options([
                        'Available' => 'Available',
                        'Reserved' => 'Reserved',
                        'Sold' => 'Sold',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('updateStatus')
                        ->label('Update Status')
                        ->icon('heroicon-o-check-circle')
                        ->form([
                            Forms\Components\Select::make('sales_status')
                                ->options([
                                    'Available' => 'Available',
                                    'Reserved' => 'Reserved',
                                    'Sold' => 'Sold',
                                ])
                                ->required(),
                        ])
                        ->action(function (array $data, \Illuminate\Database\Eloquent\Collection $records): void {
                            foreach ($records as $record) {
                                $record->update([
                                    'sales_status' => $data['sales_status'],
                                ]);
                            }
                        }),
                ]),
            ]);
    }
}
