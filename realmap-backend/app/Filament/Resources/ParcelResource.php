<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ParcelResource\Pages;
use App\Filament\Resources\ParcelResource\RelationManagers;
use App\Models\Parcel;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ParcelResource extends Resource
{
    protected static ?string $model = Parcel::class;

    protected static ?string $navigationIcon = 'heroicon-o-map';

    protected static ?string $navigationLabel = 'Parcels';

    protected static ?string $navigationGroup = 'Real Estate Management';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('parcel_number')
                    ->required(),
                Forms\Components\Select::make('block_id')
                    ->relationship('block', 'block_number')
                    ->required(),
                Forms\Components\Textarea::make('coordinates')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('plot_no'),
                Forms\Components\TextInput::make('property_type'),
                Forms\Components\TextInput::make('usage_type'),
                Forms\Components\Select::make('sales_status')
                    ->options([
                        'Available' => 'Available',
                        'Reserved' => 'Reserved',
                        'Sold' => 'Sold',
                    ])
                    ->default('Available')
                    ->required(),
                Forms\Components\TextInput::make('plot_condition'),
                Forms\Components\TextInput::make('view'),
                Forms\Components\TextInput::make('area')
                    ->numeric(),
                Forms\Components\TextInput::make('price_per_sqm')
                    ->numeric(),
                Forms\Components\TextInput::make('price_with_tax')
                    ->numeric(),
                Forms\Components\TextInput::make('expected_roi'),
                Forms\Components\DatePicker::make('delivery_date'),
                Forms\Components\TextInput::make('down_payment'),
                Forms\Components\TextInput::make('payment_terms'),
                Forms\Components\TextInput::make('infrastructure_status'),
                Forms\Components\TextInput::make('services_available'),

                Forms\Components\Section::make('Images')
                    ->schema([
                        Forms\Components\FileUpload::make('images')
                            ->label('Parcel Images')
                            ->multiple()
                            ->image()
                            ->directory('parcel-images')
                            ->disk('public')
                            ->reorderable()
                            ->appendFiles()
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ])
                            ->maxFiles(10)
                            ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/webp'])
                            ->maxSize(5120) // 5MB
                            ->helperText('Upload up to 10 images. Supported formats: JPEG, PNG, WebP. Max size: 5MB per image.')
                            ->columnSpanFull(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('parcel_number')
                    ->searchable(),
                Tables\Columns\TextColumn::make('block.block_number')
                    ->sortable()
                    ->label('Block'),
                Tables\Columns\TextColumn::make('plot_no')
                    ->searchable(),
                Tables\Columns\TextColumn::make('property_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('usage_type')
                    ->searchable(),
                Tables\Columns\TextColumn::make('sales_status')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Available' => 'success',
                        'Reserved' => 'warning',
                        'Sold' => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('plot_condition')
                    ->searchable(),
                Tables\Columns\TextColumn::make('view')
                    ->searchable(),
                Tables\Columns\TextColumn::make('area')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price_per_sqm')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('price_with_tax')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('expected_roi')
                    ->searchable(),
                Tables\Columns\TextColumn::make('delivery_date')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('down_payment')
                    ->searchable(),
                Tables\Columns\TextColumn::make('payment_terms')
                    ->searchable(),
                Tables\Columns\TextColumn::make('infrastructure_status')
                    ->searchable(),
                Tables\Columns\TextColumn::make('services_available')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('sales_status')
                    ->options([
                        'Available' => 'Available',
                        'Reserved' => 'Reserved',
                        'Sold' => 'Sold',
                    ]),
                Tables\Filters\SelectFilter::make('block_id')
                    ->relationship('block', 'block_number')
                    ->label('Block'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListParcels::route('/'),
            'create' => Pages\CreateParcel::route('/create'),
            'edit' => Pages\EditParcel::route('/{record}/edit'),
        ];
    }
}
