<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Block;
use App\Models\RealEstateProject;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class BlockController extends Controller
{
    /**
     * Get blocks as GeoJSON for a specific project.
     */
    public function getGeoJson(string $projectId): JsonResponse
    {
        $project = RealEstateProject::findOrFail($projectId);
        $blocks = Block::where('project_id', $projectId)->get();

        $features = $blocks->map(function ($block) {
            return [
                'type' => 'Feature',
                'id' => $block->id,
                'geometry' => json_decode($block->coordinates),
                'properties' => [
                    'ID' => $block->id,
                    'BlockNo' => $block->block_number,
                    'Centroid_X' => $block->centroid_x,
                    'Centroid_Y' => $block->centroid_y,
                    'Shape_Length' => $block->shape_length,
                    'Shape_Area' => $block->shape_area,
                ],
            ];
        });

        $geoJson = [
            'type' => 'FeatureCollection',
            'features' => $features,
        ];

        return response()->json($geoJson);
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $projectId = $request->query('project_id');

        $query = Block::query();

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        $blocks = $query->with('project:id,name')->get();

        return response()->json([
            'success' => true,
            'data' => $blocks,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $block = Block::with(['parcels', 'project:id,name'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $block,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
