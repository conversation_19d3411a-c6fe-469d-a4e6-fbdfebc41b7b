<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Landmark;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LandmarkController extends Controller
{
    /**
     * Get all landmarks for a specific project.
     */
    public function getProjectLandmarks(string $projectId): JsonResponse
    {
        $landmarks = Landmark::where('project_id', $projectId)
            ->where('is_active', true)
            ->orderBy('display_order')
            ->get();

        // Add the full URL for each icon path
        $landmarks->each(function ($landmark) {
            $landmark->icon_url = $landmark->icon_url;
        });

        return response()->json([
            'success' => true,
            'data' => $landmarks,
        ]);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $projectId = $request->query('project_id');
        $type = $request->query('type');

        $query = Landmark::query()->where('is_active', true);

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        if ($type) {
            $query->where('type', $type);
        }

        $landmarks = $query->with('project:id,name')
            ->orderBy('display_order')
            ->get();

        // Add the full URL for each icon path
        $landmarks->each(function ($landmark) {
            $landmark->icon_url = $landmark->icon_url;
        });

        return response()->json([
            'success' => true,
            'data' => $landmarks,
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $landmark = Landmark::with('project:id,name')->findOrFail($id);

        // Add the full URL for the icon path
        $landmark->icon_url = $landmark->icon_url;

        return response()->json([
            'success' => true,
            'data' => $landmark,
        ]);
    }
}
