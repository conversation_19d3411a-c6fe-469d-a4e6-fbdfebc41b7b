<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Block;
use App\Models\Parcel;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ParcelController extends Controller
{
    /**
     * Get parcels as GeoJSON for a specific block.
     */
    public function getGeoJson(string $blockId): JsonResponse
    {
        $block = Block::findOrFail($blockId);
        $parcels = Parcel::where('block_id', $blockId)->with(['images', 'block.project:id,name,developer_name,city,geo_area,default_unit_image'])->get();

        $features = $parcels->map(function ($parcel) {
            return [
                'type' => 'Feature',
                'geometry' => json_decode($parcel->coordinates),
                'properties' => [
                    'id' => $parcel->id,
                    'parcel_number' => $parcel->parcel_number,
                    'Parcel_No' => $parcel->parcel_number,
                    'developer_name' => $parcel->block->project->developer_name ?? '',
                    'project_name' => $parcel->block->project->name ?? '',
                    'geo_area' => $parcel->block->project->geo_area ?? '',
                    'city' => $parcel->block->project->city ?? '',
                    'property_type' => $parcel->property_type,
                    'land_status' => $parcel->sales_status,
                    'delivery_date' => $parcel->delivery_date ? $parcel->delivery_date->format('Y-m-d') : null,
                    'down_payment' => $parcel->down_payment,
                    'payment_terms' => $parcel->payment_terms,
                    'infrastructure_status' => $parcel->infrastructure_status,
                    'services_available' => $parcel->services_available,
                    'usage_type' => $parcel->usage_type,
                    'area' => $parcel->area,
                    'total_area' => $parcel->area . ' sqm',
                    'price_per_sqm' => $parcel->price_per_sqm,
                    'price_with_tax' => $parcel->price_with_tax,
                    'sales_status' => $parcel->sales_status,
                    'plot_condition' => $parcel->plot_condition,
                    'expected_roi' => $parcel->expected_roi,
                    'view' => $parcel->view,
                    'Block_No' => $parcel->block->block_number,
                    'images' => $parcel->image_urls,
                ],
            ];
        });

        $geoJson = [
            'type' => 'FeatureCollection',
            'features' => $features,
        ];

        return response()->json($geoJson);
    }

    /**
     * Search for parcels by parcel number.
     */
    public function search(Request $request): JsonResponse
    {
        $query = $request->query('q');

        if (!$query) {
            return response()->json([
                'success' => false,
                'message' => 'Search query is required',
            ], 400);
        }

        $parcels = Parcel::where('parcel_number', 'like', "%{$query}%")
            ->with('block:id,block_number,project_id')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $parcels,
        ]);
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $blockId = $request->query('block_id');
        $status = $request->query('status');
        $minPrice = $request->query('min_price');
        $maxPrice = $request->query('max_price');
        $services = $request->query('services');

        $query = Parcel::query();

        if ($blockId) {
            $query->where('block_id', $blockId);
        }

        if ($status) {
            if (is_array($status)) {
                $query->whereIn('sales_status', $status);
            } else {
                $query->where('sales_status', $status);
            }
        }

        if ($minPrice !== null) {
            $query->where('price_with_tax', '>=', $minPrice);
        }

        if ($maxPrice !== null) {
            $query->where('price_with_tax', '<=', $maxPrice);
        }

        if ($services) {
            $servicesArray = is_array($services) ? $services : [$services];
            foreach ($servicesArray as $service) {
                $query->where('services_available', 'like', '%' . $service . '%');
            }
        }

        $parcels = $query->with(['block:id,block_number,project_id', 'block.project:id,name,developer_name,city,geo_area,default_unit_image', 'images'])->get();

        return response()->json([
            'success' => true,
            'data' => $parcels,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $parcel = Parcel::with(['block:id,block_number,project_id', 'block.project:id,name,developer_name,city,geo_area,default_unit_image', 'images'])->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $parcel,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
