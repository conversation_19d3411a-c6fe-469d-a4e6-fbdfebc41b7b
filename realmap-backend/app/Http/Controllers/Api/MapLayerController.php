<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\MapLayer;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;

class MapLayerController extends Controller
{
    /**
     * Get all layers for a specific project.
     */
    public function getProjectLayers(string $projectId): JsonResponse
    {
        $layers = MapLayer::where('project_id', $projectId)
            ->where('is_active', true)
            ->orderBy('display_order')
            ->get();

        // Add the full URL for each file path
        $layers->each(function ($layer) {
            $layer->file_url = $layer->file_path ? Storage::url($layer->file_path) : null;
        });

        return response()->json([
            'success' => true,
            'data' => $layers,
        ]);
    }
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        $projectId = $request->query('project_id');
        $type = $request->query('type');

        $query = MapLayer::query()->where('is_active', true);

        if ($projectId) {
            $query->where('project_id', $projectId);
        }

        if ($type) {
            $query->where('type', $type);
        }

        $layers = $query->with('project:id,name')
            ->orderBy('display_order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $layers,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $layer = MapLayer::with('project:id,name')->findOrFail($id);

        // Add the full URL for the file path
        $layer->file_url = $layer->file_path ? Storage::url($layer->file_path) : null;

        return response()->json([
            'success' => true,
            'data' => $layer,
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }

    /**
     * Serve map layer files with proper CORS headers.
     */
    public function serveFile(string $filename)
    {
        $filePath = storage_path('app/public/map-layers/' . $filename);

        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        $mimeType = mime_content_type($filePath);

        return response()->file($filePath, [
            'Content-Type' => $mimeType,
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Methods' => 'GET, OPTIONS',
            'Access-Control-Allow-Headers' => 'Content-Type, Authorization',
        ]);
    }
}
