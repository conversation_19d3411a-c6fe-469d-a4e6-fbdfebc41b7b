<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\RealEstateProject;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class RealEstateProjectController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        $projects = RealEstateProject::with('user:id,name')->get();

        // Add developer logo URL, default unit image URL, and statistics to each project
        $projects->each(function ($project) {
            $project->developer_logo_url = $project->developer_logo_url;
            $project->default_unit_image_url = $project->default_unit_image_url;

            // Add basic statistics for quick access
            $project->statistics = $this->getProjectStatistics($project->id);
        });

        return response()->json([
            'success' => true,
            'data' => $projects,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $project = RealEstateProject::with(['blocks', 'mapLayers'])->findOrFail($id);

        // Add developer logo URL and default unit image URL
        $project->developer_logo_url = $project->developer_logo_url;
        $project->default_unit_image_url = $project->default_unit_image_url;

        // Get project statistics
        $stats = $this->getProjectStatistics($id);
        $project->statistics = $stats;

        return response()->json([
            'success' => true,
            'data' => $project,
        ]);
    }

    /**
     * Get project statistics including price ranges and unit counts.
     */
    public function getProjectStatistics(string $projectId): array
    {
        // Cache statistics for 1 hour to improve performance
        return cache()->remember("project_stats_{$projectId}", 3600, function () use ($projectId) {
            // Get all parcels for this project through blocks
            $parcels = \App\Models\Parcel::whereHas('block', function ($query) use ($projectId) {
                $query->where('project_id', $projectId);
            })->get();

        if ($parcels->isEmpty()) {
            return [
                'total_units' => 0,
                'available_units' => 0,
                'reserved_units' => 0,
                'sold_units' => 0,
                'min_price' => null,
                'max_price' => null,
                'avg_price' => null,
                'total_area' => 0,
                'avg_area' => null,
            ];
        }

        // Calculate statistics
        $totalUnits = $parcels->count();
        $availableUnits = $parcels->where('sales_status', 'Available')->count();
        $reservedUnits = $parcels->where('sales_status', 'Reserved')->count();
        $soldUnits = $parcels->where('sales_status', 'Sold')->count();

        // Price statistics (using price_with_tax or fallback to calculated price)
        $prices = $parcels->map(function ($parcel) {
            return $parcel->price_with_tax ?? ($parcel->area * $parcel->price_per_sqm);
        })->filter()->values();

        $minPrice = $prices->isNotEmpty() ? $prices->min() : null;
        $maxPrice = $prices->isNotEmpty() ? $prices->max() : null;
        $avgPrice = $prices->isNotEmpty() ? $prices->avg() : null;

        // Area statistics
        $areas = $parcels->pluck('area')->filter();
        $totalArea = $areas->sum();
        $avgArea = $areas->isNotEmpty() ? $areas->avg() : null;

            return [
                'total_units' => $totalUnits,
                'available_units' => $availableUnits,
                'reserved_units' => $reservedUnits,
                'sold_units' => $soldUnits,
                'min_price' => $minPrice,
                'max_price' => $maxPrice,
                'avg_price' => $avgPrice,
                'total_area' => $totalArea,
                'avg_area' => $avgArea,
            ];
        });
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        //
    }
}
