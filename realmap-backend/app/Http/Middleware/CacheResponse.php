<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\Response;

class CacheResponse
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  int  $minutes
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function handle(Request $request, Closure $next, int $minutes = 10080): Response // 10080 minutes = 1 week
    {
        // Only cache GET requests
        if ($request->method() !== 'GET') {
            return $next($request);
        }

        // Create cache key from request URL and parameters
        $cacheKey = 'api_cache:' . md5($request->fullUrl());

        // Try to get cached response
        $cachedResponse = Cache::get($cacheKey);
        
        if ($cachedResponse) {
            // Return cached response with cache headers
            return response($cachedResponse['content'])
                ->header('Content-Type', $cachedResponse['content_type'])
                ->header('X-Cache', 'HIT')
                ->header('Cache-Control', 'public, max-age=' . ($minutes * 60));
        }

        // Get fresh response
        $response = $next($request);

        // Only cache successful responses
        if ($response->getStatusCode() === 200) {
            $cacheData = [
                'content' => $response->getContent(),
                'content_type' => $response->headers->get('Content-Type', 'application/json'),
                'cached_at' => now()->toISOString(),
            ];

            // Cache for specified minutes (default 1 week)
            Cache::put($cacheKey, $cacheData, now()->addMinutes($minutes));

            // Add cache headers to response
            $response->header('X-Cache', 'MISS')
                    ->header('Cache-Control', 'public, max-age=' . ($minutes * 60));
        }

        return $response;
    }
}
