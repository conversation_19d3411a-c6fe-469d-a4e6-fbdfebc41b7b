<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class ClearApiCache extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:clear-api {pattern?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear API response cache. Optionally specify a pattern to clear specific caches.';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $pattern = $this->argument('pattern');
        
        if ($pattern) {
            // Clear specific cache pattern
            $this->clearCacheByPattern($pattern);
        } else {
            // Clear all API cache
            $this->clearAllApiCache();
        }
    }

    /**
     * Clear all API cache
     */
    private function clearAllApiCache()
    {
        $keys = Cache::getRedis()->keys('*api_cache:*');
        
        if (empty($keys)) {
            $this->info('No API cache found to clear.');
            return;
        }

        $count = 0;
        foreach ($keys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
            $count++;
        }

        $this->info("Cleared {$count} API cache entries.");
    }

    /**
     * Clear cache by pattern
     */
    private function clearCacheByPattern($pattern)
    {
        $keys = Cache::getRedis()->keys("*api_cache:*{$pattern}*");
        
        if (empty($keys)) {
            $this->info("No API cache found matching pattern: {$pattern}");
            return;
        }

        $count = 0;
        foreach ($keys as $key) {
            Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
            $count++;
        }

        $this->info("Cleared {$count} API cache entries matching pattern: {$pattern}");
    }
}
