<?php

namespace App\Services;

use App\Models\Block;
use App\Models\Parcel;
use App\Models\RealEstateProject;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;

class GeoJsonImportService
{
    /**
     * Import blocks from uploaded GeoJSON file
     */
    public function importBlocks(RealEstateProject $project, string $filePath): array
    {
        try {
            $fileContent = Storage::disk('local')->get($filePath);
            $geoJsonData = json_decode($fileContent, true);

            if (!$geoJsonData || !isset($geoJsonData['features'])) {
                throw new \Exception('Invalid GeoJSON format for blocks file');
            }

            $importedCount = 0;
            $errors = [];

            foreach ($geoJsonData['features'] as $feature) {
                try {
                    $properties = $feature['properties'] ?? [];
                    $geometry = $feature['geometry'] ?? null;

                    if (!$geometry) {
                        $errors[] = 'Missing geometry for block feature';
                        continue;
                    }

                    // Extract block number from various possible property names
                    $blockNumber = $properties['BlockNo'] ??
                        $properties['Block_No'] ??
                        $properties['block_number'] ??
                        $properties['BLOCK_NO'] ??
                        'Block_' . ($importedCount + 1);

                    Block::create([
                        'block_number' => $blockNumber,
                        'project_id' => $project->id,
                        'coordinates' => json_encode($geometry),
                        'shape_length' => $properties['Shape_Length'] ?? $properties['shape_length'] ?? 0,
                        'shape_area' => $properties['Shape_Area'] ?? $properties['shape_area'] ?? 0,
                        'centroid_x' => $properties['Centroid_X'] ?? $properties['centroid_x'] ?? 0,
                        'centroid_y' => $properties['Centroid_Y'] ?? $properties['centroid_y'] ?? 0,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = 'Error importing block: ' . $e->getMessage();
                    Log::error('Block import error', ['error' => $e->getMessage(), 'feature' => $feature]);
                }
            }

            return [
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors,
                'message' => "Successfully imported {$importedCount} blocks"
            ];
        } catch (\Exception $e) {
            Log::error('Blocks GeoJSON import failed', ['error' => $e->getMessage(), 'file' => $filePath]);
            return [
                'success' => false,
                'imported_count' => 0,
                'errors' => [$e->getMessage()],
                'message' => 'Failed to import blocks: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import parcels from uploaded GeoJSON file
     */
    public function importParcels(RealEstateProject $project, string $filePath): array
    {
        try {
            $fileContent = Storage::disk('local')->get($filePath);
            $geoJsonData = json_decode($fileContent, true);

            if (!$geoJsonData || !isset($geoJsonData['features'])) {
                throw new \Exception('Invalid GeoJSON format for parcels file');
            }

            $importedCount = 0;
            $errors = [];

            foreach ($geoJsonData['features'] as $feature) {
                try {
                    $properties = $feature['properties'] ?? [];
                    $geometry = $feature['geometry'] ?? null;

                    if (!$geometry) {
                        $errors[] = 'Missing geometry for parcel feature';
                        continue;
                    }

                    // Extract block number to find corresponding block
                    $blockNumber = $properties['Block_No'] ??
                        $properties['BlockNo'] ??
                        $properties['block_number'] ??
                        $properties['BLOCK_NO'] ?? null;

                    if (!$blockNumber) {
                        $errors[] = 'Missing block number for parcel: ' . ($properties['Parcel_No'] ?? 'Unknown');
                        continue;
                    }

                    // Find the corresponding block
                    $block = Block::where('project_id', $project->id)
                        ->where('block_number', $blockNumber)
                        ->first();

                    if (!$block) {
                        $errors[] = "Block not found for parcel: {$blockNumber}";
                        continue;
                    }

                    // Extract parcel number
                    $parcelNumber = $properties['Parcel_No'] ??
                        $properties['parcel_number'] ??
                        $properties['PARCEL_NO'] ??
                        'Parcel_' . ($importedCount + 1);

                    // Extract area from total_area string (e.g., "984 sqm" -> 984)
                    $area = 0;
                    if (isset($properties['total_area'])) {
                        preg_match('/(\d+(?:\.\d+)?)/', $properties['total_area'], $matches);
                        $area = isset($matches[1]) ? (float)$matches[1] : 0;
                    } elseif (isset($properties['area'])) {
                        $area = (float)$properties['area'];
                    }

                    // Extract price per sqm
                    $pricePerSqm = 0;
                    if (isset($properties['price_per_sqm'])) {
                        preg_match('/(\d+(?:\.\d+)?)/', $properties['price_per_sqm'], $matches);
                        $pricePerSqm = isset($matches[1]) ? (float)$matches[1] : 0;
                    }

                    // Extract total price
                    $priceWithTax = 0;
                    if (isset($properties['price_with_tax'])) {
                        preg_match('/(\d+(?:\.\d+)?)/', $properties['price_with_tax'], $matches);
                        $priceWithTax = isset($matches[1]) ? (float)$matches[1] : 0;
                    }

                    Parcel::create([
                        'parcel_number' => $parcelNumber,
                        'block_id' => $block->id,
                        'coordinates' => json_encode($geometry),
                        'plot_no' => $properties['plot_no'] ?? null,
                        'property_type' => $properties['property_type'] ?? null,
                        'usage_type' => $properties['usage_type'] ?? null,
                        'sales_status' => $properties['land_status'] ?? $properties['sales_status'] ?? 'Available',
                        'plot_condition' => $properties['plot_condition'] ?? null,
                        'view' => $properties['view'] ?? null,
                        'area' => $area,
                        'price_per_sqm' => $pricePerSqm,
                        'price_with_tax' => $priceWithTax,
                        'expected_roi' => $properties['expected_roi'] ?? null,
                        'delivery_date' => isset($properties['delivery_date']) ?
                            \Carbon\Carbon::parse($properties['delivery_date'])->format('Y-m-d') : null,
                        'down_payment' => $properties['down_payment'] ?? null,
                        'payment_terms' => $properties['payment_terms'] ?? null,
                        'infrastructure_status' => $properties['infrastructure_status'] ?? null,
                        'services_available' => $properties['services_available'] ?? null,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = 'Error importing parcel: ' . $e->getMessage();
                    Log::error('Parcel import error', ['error' => $e->getMessage(), 'feature' => $feature]);
                }
            }

            return [
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors,
                'message' => "Successfully imported {$importedCount} parcels"
            ];
        } catch (\Exception $e) {
            Log::error('Parcels GeoJSON import failed', ['error' => $e->getMessage(), 'file' => $filePath]);
            return [
                'success' => false,
                'imported_count' => 0,
                'errors' => [$e->getMessage()],
                'message' => 'Failed to import parcels: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate GeoJSON file format
     */
    public function validateGeoJsonFile(string $filePath): array
    {
        try {
            $fileContent = Storage::disk('local')->get($filePath);
            $geoJsonData = json_decode($fileContent, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                return [
                    'valid' => false,
                    'error' => 'Invalid JSON format: ' . json_last_error_msg()
                ];
            }

            if (!isset($geoJsonData['type']) || $geoJsonData['type'] !== 'FeatureCollection') {
                return [
                    'valid' => false,
                    'error' => 'Invalid GeoJSON: Must be a FeatureCollection'
                ];
            }

            if (!isset($geoJsonData['features']) || !is_array($geoJsonData['features'])) {
                return [
                    'valid' => false,
                    'error' => 'Invalid GeoJSON: Missing or invalid features array'
                ];
            }

            return [
                'valid' => true,
                'features_count' => count($geoJsonData['features'])
            ];
        } catch (\Exception $e) {
            return [
                'valid' => false,
                'error' => 'Error reading file: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import blocks from uploaded file (temporary file)
     */
    public function importBlocksFromUploadedFile(RealEstateProject $project, UploadedFile $file): array
    {
        try {
            $fileContent = file_get_contents($file->getRealPath());
            $geoJsonData = json_decode($fileContent, true);

            if (!$geoJsonData || !isset($geoJsonData['features'])) {
                throw new \Exception('Invalid GeoJSON format for blocks file');
            }

            $importedCount = 0;
            $errors = [];

            foreach ($geoJsonData['features'] as $feature) {
                try {
                    $properties = $feature['properties'] ?? [];
                    $geometry = $feature['geometry'] ?? null;

                    if (!$geometry) {
                        $errors[] = 'Missing geometry for block feature';
                        continue;
                    }

                    // Extract block number from various possible property names
                    $blockNumber = $properties['BlockNo'] ??
                        $properties['Block_No'] ??
                        $properties['block_number'] ??
                        $properties['BLOCK_NO'] ??
                        'Block_' . ($importedCount + 1);

                    Block::create([
                        'block_number' => $blockNumber,
                        'project_id' => $project->id,
                        'coordinates' => json_encode($geometry),
                        'shape_length' => $properties['Shape_Length'] ?? $properties['shape_length'] ?? 0,
                        'shape_area' => $properties['Shape_Area'] ?? $properties['shape_area'] ?? 0,
                        'centroid_x' => $properties['Centroid_X'] ?? $properties['centroid_x'] ?? 0,
                        'centroid_y' => $properties['Centroid_Y'] ?? $properties['centroid_y'] ?? 0,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = 'Error importing block: ' . $e->getMessage();
                    Log::error('Block import error', ['error' => $e->getMessage(), 'feature' => $feature]);
                }
            }

            return [
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors,
                'message' => "Successfully imported {$importedCount} blocks"
            ];
        } catch (\Exception $e) {
            Log::error('Blocks GeoJSON import failed', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'imported_count' => 0,
                'errors' => [$e->getMessage()],
                'message' => 'Failed to import blocks: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Import parcels from uploaded file (temporary file)
     */
    public function importParcelsFromUploadedFile(RealEstateProject $project, UploadedFile $file): array
    {
        try {
            $fileContent = file_get_contents($file->getRealPath());
            $geoJsonData = json_decode($fileContent, true);

            if (!$geoJsonData || !isset($geoJsonData['features'])) {
                throw new \Exception('Invalid GeoJSON format for parcels file');
            }

            $importedCount = 0;
            $errors = [];

            foreach ($geoJsonData['features'] as $feature) {
                try {
                    $properties = $feature['properties'] ?? [];
                    $geometry = $feature['geometry'] ?? null;

                    if (!$geometry) {
                        $errors[] = 'Missing geometry for parcel feature';
                        continue;
                    }

                    // Extract block number to find corresponding block
                    $blockNumber = $properties['Block_No'] ??
                        $properties['BlockNo'] ??
                        $properties['block_number'] ??
                        $properties['BLOCK_NO'] ?? null;

                    if (!$blockNumber) {
                        $errors[] = 'Missing block number for parcel: ' . ($properties['Parcel_No'] ?? 'Unknown');
                        continue;
                    }

                    // Find the corresponding block
                    $block = Block::where('project_id', $project->id)
                        ->where('block_number', $blockNumber)
                        ->first();

                    if (!$block) {
                        $errors[] = "Block not found for parcel: {$blockNumber}";
                        continue;
                    }

                    // Extract parcel number
                    $parcelNumber = $properties['Parcel_No'] ??
                        $properties['parcel_number'] ??
                        $properties['PARCEL_NO'] ??
                        'Parcel_' . ($importedCount + 1);

                    // Extract area from total_area string (e.g., "984 sqm" -> 984)
                    $area = 0;
                    if (isset($properties['total_area'])) {
                        preg_match('/(\d+(?:\.\d+)?)/', $properties['total_area'], $matches);
                        $area = isset($matches[1]) ? (float)$matches[1] : 0;
                    } elseif (isset($properties['area'])) {
                        $area = (float)$properties['area'];
                    }

                    // Extract price per sqm
                    $pricePerSqm = 0;
                    if (isset($properties['price_per_sqm'])) {
                        preg_match('/(\d+(?:\.\d+)?)/', $properties['price_per_sqm'], $matches);
                        $pricePerSqm = isset($matches[1]) ? (float)$matches[1] : 0;
                    }

                    // Extract total price
                    $priceWithTax = 0;
                    if (isset($properties['price_with_tax'])) {
                        preg_match('/(\d+(?:\.\d+)?)/', $properties['price_with_tax'], $matches);
                        $priceWithTax = isset($matches[1]) ? (float)$matches[1] : 0;
                    }

                    Parcel::create([
                        'parcel_number' => $parcelNumber,
                        'block_id' => $block->id,
                        'coordinates' => json_encode($geometry),
                        'plot_no' => $properties['plot_no'] ?? null,
                        'property_type' => $properties['property_type'] ?? null,
                        'usage_type' => $properties['usage_type'] ?? null,
                        'sales_status' => $properties['land_status'] ?? $properties['sales_status'] ?? 'Available',
                        'plot_condition' => $properties['plot_condition'] ?? null,
                        'view' => $properties['view'] ?? null,
                        'area' => $area,
                        'price_per_sqm' => $pricePerSqm,
                        'price_with_tax' => $priceWithTax,
                        'expected_roi' => $properties['expected_roi'] ?? null,
                        'delivery_date' => isset($properties['delivery_date']) ?
                            \Carbon\Carbon::parse($properties['delivery_date'])->format('Y-m-d') : null,
                        'down_payment' => $properties['down_payment'] ?? null,
                        'payment_terms' => $properties['payment_terms'] ?? null,
                        'infrastructure_status' => $properties['infrastructure_status'] ?? null,
                        'services_available' => $properties['services_available'] ?? null,
                    ]);

                    $importedCount++;
                } catch (\Exception $e) {
                    $errors[] = 'Error importing parcel: ' . $e->getMessage();
                    Log::error('Parcel import error', ['error' => $e->getMessage(), 'feature' => $feature]);
                }
            }

            return [
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors,
                'message' => "Successfully imported {$importedCount} parcels"
            ];
        } catch (\Exception $e) {
            Log::error('Parcels GeoJSON import failed', ['error' => $e->getMessage()]);
            return [
                'success' => false,
                'imported_count' => 0,
                'errors' => [$e->getMessage()],
                'message' => 'Failed to import parcels: ' . $e->getMessage()
            ];
        }
    }
}
