<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Parcel extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'parcel_number',
        'block_id',
        'coordinates',
        'plot_no',
        'property_type',
        'usage_type',
        'sales_status',
        'plot_condition',
        'view',
        'area',
        'price_per_sqm',
        'price_with_tax',
        'expected_roi',
        'delivery_date',
        'down_payment',
        'payment_terms',
        'infrastructure_status',
        'services_available',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'coordinates' => 'json',
        'area' => 'decimal:2',
        'price_per_sqm' => 'decimal:2',
        'price_with_tax' => 'decimal:2',
        'delivery_date' => 'date',
    ];

    /**
     * Get the block that owns the parcel.
     */
    public function block(): BelongsTo
    {
        return $this->belongsTo(Block::class);
    }

    /**
     * Get the images for the parcel.
     */
    public function images(): HasMany
    {
        return $this->hasMany(ParcelImage::class)->ordered();
    }

    /**
     * Get the primary image for the parcel.
     */
    public function primaryImage()
    {
        return $this->hasOne(ParcelImage::class)->primary();
    }

    /**
     * Get all image URLs for the parcel.
     */
    public function getImageUrlsAttribute(): array
    {
        $parcelImages = $this->images->map(function ($image) {
            return [
                'id' => $image->id,
                'url' => $image->image_url,
                'alt_text' => $image->alt_text,
                'is_primary' => $image->is_primary,
                'original_name' => $image->original_name,
            ];
        })->toArray();

        // If no parcel images exist, use the project's default unit image
        if (empty($parcelImages) && $this->block && $this->block->project && $this->block->project->default_unit_image_url) {
            return [
                [
                    'id' => null,
                    'url' => $this->block->project->default_unit_image_url,
                    'alt_text' => 'Default unit image',
                    'is_primary' => true,
                    'original_name' => 'default_unit_image',
                ]
            ];
        }

        return $parcelImages;
    }
}
