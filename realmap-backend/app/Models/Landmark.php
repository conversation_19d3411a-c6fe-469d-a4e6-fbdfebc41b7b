<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Landmark extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'type',
        'project_id',
        'latitude',
        'longitude',
        'icon_path',
        'description',
        'is_active',
        'min_zoom',
        'max_zoom',
        'display_order',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_active' => 'boolean',
        'min_zoom' => 'integer',
        'max_zoom' => 'integer',
        'display_order' => 'integer',
    ];

    /**
     * Get the project that owns the landmark.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(RealEstateProject::class, 'project_id');
    }

    /**
     * Get the full URL for the icon.
     */
    public function getIconUrlAttribute(): ?string
    {
        if (!$this->icon_path) {
            return null;
        }

        return url('storage/' . $this->icon_path);
    }

    /**
     * Get landmark type options.
     */
    public static function getTypeOptions(): array
    {
        return [
            'mosque' => 'مسجد',
            'school' => 'مدرسة',
            'hospital' => 'مستشفى',
            'shopping_center' => 'مركز تسوق',
            'park' => 'حديقة',
            'gas_station' => 'محطة وقود',
            'restaurant' => 'مطعم',
            'bank' => 'بنك',
            'pharmacy' => 'صيدلية',
            'other' => 'أخرى',
        ];
    }
}
