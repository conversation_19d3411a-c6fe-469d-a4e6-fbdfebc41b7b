<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Block extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'block_number',
        'project_id',
        'coordinates',
        'shape_length',
        'shape_area',
        'centroid_x',
        'centroid_y',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'coordinates' => 'json',
        'shape_length' => 'decimal:8',
        'shape_area' => 'decimal:8',
        'centroid_x' => 'decimal:8',
        'centroid_y' => 'decimal:8',
    ];

    /**
     * Get the project that owns the block.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(RealEstateProject::class, 'project_id');
    }

    /**
     * Get the parcels for the block.
     */
    public function parcels(): HasMany
    {
        return $this->hasMany(Parcel::class);
    }
}
